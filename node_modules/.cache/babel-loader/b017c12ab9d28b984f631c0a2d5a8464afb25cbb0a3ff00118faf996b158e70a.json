{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, MagnifyingGlassIcon, FunnelIcon, DocumentArrowUpIcon } from '@heroicons/react/24/outline';\nimport ScholarshipForm from '../../admin/components/ScholarshipForm';\nimport Modal from '../../admin/components/Modal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScholarshipManager = () => {\n  _s();\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterLevel, setFilterLevel] = useState('');\n  const [filterCountry, setFilterCountry] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedScholarship, setSelectedScholarship] = useState(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  useEffect(() => {\n    fetchScholarships();\n  }, []);\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('http://localhost:5000/api/scholarships', {\n        credentials: 'include'\n      });\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const result = await response.json();\n      const scholarshipsData = result.data || result.scholarships || [];\n      setScholarships(scholarshipsData);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateScholarship = async formData => {\n    try {\n      setIsSubmitting(true);\n\n      // Create FormData for multipart submission (industry standard)\n      const submitData = new FormData();\n\n      // Add all form fields\n      Object.keys(formData).forEach(key => {\n        if (key === 'thumbnail' && formData[key] && formData[key].startsWith('data:image/')) {\n          // Handle base64 images - backend will convert to files\n          submitData.append(key, formData[key]);\n        } else if (formData[key] !== null && formData[key] !== undefined) {\n          submitData.append(key, formData[key]);\n        }\n      });\n      const response = await fetch('http://localhost:5000/api/scholarships', {\n        method: 'POST',\n        body: submitData,\n        credentials: 'include'\n      });\n      if (!response.ok) {\n        throw new Error('Failed to create scholarship');\n      }\n      await fetchScholarships();\n      setShowAddModal(false);\n      alert('Scholarship created successfully!');\n    } catch (err) {\n      alert(err instanceof Error ? err.message : 'Failed to create scholarship');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleUpdateScholarship = async formData => {\n    if (!selectedScholarship) return;\n    try {\n      setIsSubmitting(true);\n\n      // Create FormData for multipart submission (industry standard)\n      const submitData = new FormData();\n\n      // Add all form fields\n      Object.keys(formData).forEach(key => {\n        if (key === 'thumbnail' && formData[key] && formData[key].startsWith('data:image/')) {\n          // Handle base64 images - backend will convert to files\n          submitData.append(key, formData[key]);\n        } else if (formData[key] !== null && formData[key] !== undefined) {\n          submitData.append(key, formData[key]);\n        }\n      });\n      const response = await fetch(`http://localhost:5000/api/scholarships/${selectedScholarship.id}`, {\n        method: 'PUT',\n        body: submitData,\n        credentials: 'include'\n      });\n      if (!response.ok) {\n        throw new Error('Failed to update scholarship');\n      }\n      await fetchScholarships();\n      setShowEditModal(false);\n      setSelectedScholarship(null);\n      alert('Scholarship updated successfully!');\n    } catch (err) {\n      alert(err instanceof Error ? err.message : 'Failed to update scholarship');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleDelete = async id => {\n    if (!window.confirm('Are you sure you want to delete this scholarship?')) {\n      return;\n    }\n    try {\n      const response = await fetch(`http://localhost:5000/api/scholarships/${id}`, {\n        method: 'DELETE',\n        credentials: 'include'\n      });\n      if (!response.ok) {\n        throw new Error('Failed to delete scholarship');\n      }\n      setScholarships(scholarships.filter(s => s.id !== id));\n      alert('Scholarship deleted successfully!');\n    } catch (err) {\n      alert(err instanceof Error ? err.message : 'Failed to delete scholarship');\n    }\n  };\n  const handleEdit = scholarship => {\n    setSelectedScholarship(scholarship);\n    setShowEditModal(true);\n  };\n  const handleView = scholarship => {\n    // Open scholarship detail in new tab\n    window.open(`/scholarships/${scholarship.id}`, '_blank');\n  };\n  const handleCloseModals = () => {\n    setShowAddModal(false);\n    setShowEditModal(false);\n    setSelectedScholarship(null);\n  };\n  const filteredScholarships = scholarships.filter(scholarship => {\n    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) || scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesLevel = !filterLevel || scholarship.level === filterLevel;\n    const matchesCountry = !filterCountry || scholarship.country === filterCountry;\n    return matchesSearch && matchesLevel && matchesCountry;\n  });\n  const uniqueLevels = [...new Set(scholarships.map(s => s.level))].filter(Boolean);\n  const uniqueCountries = [...new Set(scholarships.map(s => s.country))].filter(Boolean);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Scholarship Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage scholarships, add new ones, and update existing entries\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddModal(true),\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Scholarship\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(DocumentArrowUpIcon, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Bulk Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow-sm border\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n            className: \"h-5 w-5 absolute left-3 top-3 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search scholarships...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterLevel,\n          onChange: e => setFilterLevel(e.target.value),\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Levels\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), uniqueLevels.map(level => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: level,\n            children: level\n          }, level, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterCountry,\n          onChange: e => setFilterCountry(e.target.value),\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Countries\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), uniqueCountries.map(country => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: country,\n            children: country\n          }, country, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"More Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-blue-600\",\n          children: scholarships.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Total Scholarships\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-green-600\",\n          children: scholarships.filter(s => s.isOpen).length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Open Applications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-orange-600\",\n          children: scholarships.filter(s => !s.isOpen).length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Closed Applications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-purple-600\",\n          children: uniqueCountries.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Countries\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Scholarship\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Country\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Deadline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredScholarships.map(scholarship => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: scholarship.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500 truncate max-w-xs\",\n                    children: scholarship.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: scholarship.country\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                  children: scholarship.level\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: new Date(scholarship.deadline).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${scholarship.isOpen ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: scholarship.isOpen ? 'Open' : 'Closed'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleView(scholarship),\n                    className: \"text-blue-600 hover:text-blue-900\",\n                    title: \"View Scholarship\",\n                    children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(scholarship),\n                    className: \"text-green-600 hover:text-green-900\",\n                    title: \"Edit Scholarship\",\n                    children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(scholarship.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    title: \"Delete Scholarship\",\n                    children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)]\n            }, scholarship.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), filteredScholarships.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-500\",\n          children: \"No scholarships found matching your criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), showAddModal && /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: showAddModal,\n      onClose: handleCloseModals,\n      title: \"Add New Scholarship\",\n      children: [/*#__PURE__*/_jsxDEV(ScholarshipForm, {\n        scholarship: null,\n        onSubmit: handleCreateScholarship,\n        onCancel: handleCloseModals\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this), isSubmitting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-gray-600\",\n            children: \"Creating scholarship...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this), showEditModal && selectedScholarship && /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: showEditModal,\n      onClose: handleCloseModals,\n      title: \"Edit Scholarship\",\n      size: \"large\",\n      children: [/*#__PURE__*/_jsxDEV(ScholarshipForm, {\n        scholarship: selectedScholarship,\n        onSubmit: handleUpdateScholarship,\n        onCancel: handleCloseModals\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 11\n      }, this), isSubmitting && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-gray-600\",\n            children: \"Updating scholarship...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this);\n};\n_s(ScholarshipManager, \"lxXZ1eFyCYhFooAbPo0IBLInTdQ=\");\n_c = ScholarshipManager;\nexport default ScholarshipManager;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "MagnifyingGlassIcon", "FunnelIcon", "DocumentArrowUpIcon", "ScholarshipForm", "Modal", "jsxDEV", "_jsxDEV", "ScholarshipManager", "_s", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "filterLevel", "setFilterLevel", "filterCountry", "setFilterCountry", "showAddModal", "setShowAddModal", "showEditModal", "setShowEditModal", "selectedScholarship", "setSelectedScholarship", "isSubmitting", "setIsSubmitting", "fetchScholarships", "response", "fetch", "credentials", "ok", "Error", "result", "json", "scholarshipsData", "data", "err", "message", "handleCreateScholarship", "formData", "submitData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "startsWith", "append", "undefined", "method", "body", "alert", "handleUpdateScholarship", "id", "handleDelete", "window", "confirm", "filter", "s", "handleEdit", "scholarship", "handleView", "open", "handleCloseModals", "filteredScholarships", "matchesSearch", "title", "toLowerCase", "includes", "description", "matchesLevel", "level", "matchesCountry", "country", "uniqueLevels", "Set", "map", "Boolean", "uniqueCountries", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "length", "isOpen", "Date", "deadline", "toLocaleDateString", "onClose", "onSubmit", "onCancel", "size", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  DocumentArrowUpIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport ScholarshipForm from '../../admin/components/ScholarshipForm';\nimport Modal from '../../admin/components/Modal';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  country: string;\n  level: string;\n  deadline: string;\n  amount?: string;\n  isOpen: boolean;\n  thumbnail?: string;\n  coverage?: string;\n  financialBenefitsSummary?: string;\n  eligibilitySummary?: string;\n  scholarshipLink?: string;\n  youtubeLink?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst ScholarshipManager: React.FC = () => {\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterLevel, setFilterLevel] = useState('');\n  const [filterCountry, setFilterCountry] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedScholarship, setSelectedScholarship] = useState<Scholarship | null>(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  useEffect(() => {\n    fetchScholarships();\n  }, []);\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('http://localhost:5000/api/scholarships', {\n        credentials: 'include'\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const result = await response.json();\n      const scholarshipsData = result.data || result.scholarships || [];\n      setScholarships(scholarshipsData);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateScholarship = async (formData: any) => {\n    try {\n      setIsSubmitting(true);\n\n      // Create FormData for multipart submission (industry standard)\n      const submitData = new FormData();\n\n      // Add all form fields\n      Object.keys(formData).forEach(key => {\n        if (key === 'thumbnail' && formData[key] && formData[key].startsWith('data:image/')) {\n          // Handle base64 images - backend will convert to files\n          submitData.append(key, formData[key]);\n        } else if (formData[key] !== null && formData[key] !== undefined) {\n          submitData.append(key, formData[key]);\n        }\n      });\n\n      const response = await fetch('http://localhost:5000/api/scholarships', {\n        method: 'POST',\n        body: submitData,\n        credentials: 'include'\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to create scholarship');\n      }\n\n      await fetchScholarships();\n      setShowAddModal(false);\n      alert('Scholarship created successfully!');\n    } catch (err) {\n      alert(err instanceof Error ? err.message : 'Failed to create scholarship');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleUpdateScholarship = async (formData: any) => {\n    if (!selectedScholarship) return;\n\n    try {\n      setIsSubmitting(true);\n\n      // Create FormData for multipart submission (industry standard)\n      const submitData = new FormData();\n\n      // Add all form fields\n      Object.keys(formData).forEach(key => {\n        if (key === 'thumbnail' && formData[key] && formData[key].startsWith('data:image/')) {\n          // Handle base64 images - backend will convert to files\n          submitData.append(key, formData[key]);\n        } else if (formData[key] !== null && formData[key] !== undefined) {\n          submitData.append(key, formData[key]);\n        }\n      });\n\n      const response = await fetch(`http://localhost:5000/api/scholarships/${selectedScholarship.id}`, {\n        method: 'PUT',\n        body: submitData,\n        credentials: 'include'\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to update scholarship');\n      }\n\n      await fetchScholarships();\n      setShowEditModal(false);\n      setSelectedScholarship(null);\n      alert('Scholarship updated successfully!');\n    } catch (err) {\n      alert(err instanceof Error ? err.message : 'Failed to update scholarship');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this scholarship?')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`http://localhost:5000/api/scholarships/${id}`, {\n        method: 'DELETE',\n        credentials: 'include'\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to delete scholarship');\n      }\n\n      setScholarships(scholarships.filter(s => s.id !== id));\n      alert('Scholarship deleted successfully!');\n    } catch (err) {\n      alert(err instanceof Error ? err.message : 'Failed to delete scholarship');\n    }\n  };\n\n  const handleEdit = (scholarship: Scholarship) => {\n    setSelectedScholarship(scholarship);\n    setShowEditModal(true);\n  };\n\n  const handleView = (scholarship: Scholarship) => {\n    // Open scholarship detail in new tab\n    window.open(`/scholarships/${scholarship.id}`, '_blank');\n  };\n\n  const handleCloseModals = () => {\n    setShowAddModal(false);\n    setShowEditModal(false);\n    setSelectedScholarship(null);\n  };\n\n  const filteredScholarships = scholarships.filter(scholarship => {\n    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesLevel = !filterLevel || scholarship.level === filterLevel;\n    const matchesCountry = !filterCountry || scholarship.country === filterCountry;\n    \n    return matchesSearch && matchesLevel && matchesCountry;\n  });\n\n  const uniqueLevels = [...new Set(scholarships.map(s => s.level))].filter(Boolean);\n  const uniqueCountries = [...new Set(scholarships.map(s => s.country))].filter(Boolean);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Scholarship Management</h1>\n          <p className=\"text-gray-600\">Manage scholarships, add new ones, and update existing entries</p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={() => setShowAddModal(true)}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\n          >\n            <PlusIcon className=\"h-5 w-5\" />\n            <span>Add Scholarship</span>\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2\">\n            <DocumentArrowUpIcon className=\"h-5 w-5\" />\n            <span>Bulk Import</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"relative\">\n            <MagnifyingGlassIcon className=\"h-5 w-5 absolute left-3 top-3 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search scholarships...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <select\n            value={filterLevel}\n            onChange={(e) => setFilterLevel(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">All Levels</option>\n            {uniqueLevels.map(level => (\n              <option key={level} value={level}>{level}</option>\n            ))}\n          </select>\n          <select\n            value={filterCountry}\n            onChange={(e) => setFilterCountry(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">All Countries</option>\n            {uniqueCountries.map(country => (\n              <option key={country} value={country}>{country}</option>\n            ))}\n          </select>\n          <button className=\"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2\">\n            <FunnelIcon className=\"h-5 w-5\" />\n            <span>More Filters</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n          {error}\n        </div>\n      )}\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-blue-600\">{scholarships.length}</div>\n          <div className=\"text-sm text-gray-600\">Total Scholarships</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {scholarships.filter(s => s.isOpen).length}\n          </div>\n          <div className=\"text-sm text-gray-600\">Open Applications</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-orange-600\">\n            {scholarships.filter(s => !s.isOpen).length}\n          </div>\n          <div className=\"text-sm text-gray-600\">Closed Applications</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-purple-600\">{uniqueCountries.length}</div>\n          <div className=\"text-sm text-gray-600\">Countries</div>\n        </div>\n      </div>\n\n      {/* Scholarships Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Scholarship\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Country\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Level\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Deadline\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredScholarships.map((scholarship) => (\n                <tr key={scholarship.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {scholarship.title}\n                      </div>\n                      <div className=\"text-sm text-gray-500 truncate max-w-xs\">\n                        {scholarship.description}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {scholarship.country}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n                      {scholarship.level}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {new Date(scholarship.deadline).toLocaleDateString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      scholarship.isOpen\n                        ? 'bg-green-100 text-green-800'\n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {scholarship.isOpen ? 'Open' : 'Closed'}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => handleView(scholarship)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        title=\"View Scholarship\"\n                      >\n                        <EyeIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        onClick={() => handleEdit(scholarship)}\n                        className=\"text-green-600 hover:text-green-900\"\n                        title=\"Edit Scholarship\"\n                      >\n                        <PencilIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        onClick={() => handleDelete(scholarship.id)}\n                        className=\"text-red-600 hover:text-red-900\"\n                        title=\"Delete Scholarship\"\n                      >\n                        <TrashIcon className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n        \n        {filteredScholarships.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-500\">No scholarships found matching your criteria.</div>\n          </div>\n        )}\n      </div>\n\n      {/* Add Scholarship Modal */}\n      {showAddModal && (\n        <Modal\n          isOpen={showAddModal}\n          onClose={handleCloseModals}\n          title=\"Add New Scholarship\"\n        >\n          <ScholarshipForm\n            scholarship={null}\n            onSubmit={handleCreateScholarship}\n            onCancel={handleCloseModals}\n          />\n          {isSubmitting && (\n            <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                <p className=\"mt-2 text-sm text-gray-600\">Creating scholarship...</p>\n              </div>\n            </div>\n          )}\n        </Modal>\n      )}\n\n      {/* Edit Scholarship Modal */}\n      {showEditModal && selectedScholarship && (\n        <Modal\n          isOpen={showEditModal}\n          onClose={handleCloseModals}\n          title=\"Edit Scholarship\"\n          size=\"large\"\n        >\n          <ScholarshipForm\n            scholarship={selectedScholarship}\n            onSubmit={handleUpdateScholarship}\n            onCancel={handleCloseModals}\n          />\n          {isSubmitting && (\n            <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                <p className=\"mt-2 text-sm text-gray-600\">Updating scholarship...</p>\n              </div>\n            </div>\n          )}\n        </Modal>\n      )}\n    </div>\n  );\n};\n\nexport default ScholarshipManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,mBAAmB,EACnBC,UAAU,EACVC,mBAAmB,QAEd,6BAA6B;AACpC,OAAOC,eAAe,MAAM,wCAAwC;AACpE,OAAOC,KAAK,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAqBjD,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhC,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdkC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,EAAE;QACrEC,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,MAAM,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC,MAAMC,gBAAgB,GAAGF,MAAM,CAACG,IAAI,IAAIH,MAAM,CAAC1B,YAAY,IAAI,EAAE;MACjEC,eAAe,CAAC2B,gBAAgB,CAAC;IACnC,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZzB,QAAQ,CAACyB,GAAG,YAAYL,KAAK,GAAGK,GAAG,CAACC,OAAO,GAAG,mBAAmB,CAAC;IACpE,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,uBAAuB,GAAG,MAAOC,QAAa,IAAK;IACvD,IAAI;MACFd,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAMe,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAEjC;MACAC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,KAAK,WAAW,IAAIN,QAAQ,CAACM,GAAG,CAAC,IAAIN,QAAQ,CAACM,GAAG,CAAC,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;UACnF;UACAN,UAAU,CAACO,MAAM,CAACF,GAAG,EAAEN,QAAQ,CAACM,GAAG,CAAC,CAAC;QACvC,CAAC,MAAM,IAAIN,QAAQ,CAACM,GAAG,CAAC,KAAK,IAAI,IAAIN,QAAQ,CAACM,GAAG,CAAC,KAAKG,SAAS,EAAE;UAChER,UAAU,CAACO,MAAM,CAACF,GAAG,EAAEN,QAAQ,CAACM,GAAG,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;MAEF,MAAMlB,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,EAAE;QACrEqB,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEV,UAAU;QAChBX,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAML,iBAAiB,CAAC,CAAC;MACzBP,eAAe,CAAC,KAAK,CAAC;MACtBgC,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOf,GAAG,EAAE;MACZe,KAAK,CAACf,GAAG,YAAYL,KAAK,GAAGK,GAAG,CAACC,OAAO,GAAG,8BAA8B,CAAC;IAC5E,CAAC,SAAS;MACRZ,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM2B,uBAAuB,GAAG,MAAOb,QAAa,IAAK;IACvD,IAAI,CAACjB,mBAAmB,EAAE;IAE1B,IAAI;MACFG,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAMe,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAEjC;MACAC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,KAAK,WAAW,IAAIN,QAAQ,CAACM,GAAG,CAAC,IAAIN,QAAQ,CAACM,GAAG,CAAC,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;UACnF;UACAN,UAAU,CAACO,MAAM,CAACF,GAAG,EAAEN,QAAQ,CAACM,GAAG,CAAC,CAAC;QACvC,CAAC,MAAM,IAAIN,QAAQ,CAACM,GAAG,CAAC,KAAK,IAAI,IAAIN,QAAQ,CAACM,GAAG,CAAC,KAAKG,SAAS,EAAE;UAChER,UAAU,CAACO,MAAM,CAACF,GAAG,EAAEN,QAAQ,CAACM,GAAG,CAAC,CAAC;QACvC;MACF,CAAC,CAAC;MAEF,MAAMlB,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0CN,mBAAmB,CAAC+B,EAAE,EAAE,EAAE;QAC/FJ,MAAM,EAAE,KAAK;QACbC,IAAI,EAAEV,UAAU;QAChBX,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAML,iBAAiB,CAAC,CAAC;MACzBL,gBAAgB,CAAC,KAAK,CAAC;MACvBE,sBAAsB,CAAC,IAAI,CAAC;MAC5B4B,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOf,GAAG,EAAE;MACZe,KAAK,CAACf,GAAG,YAAYL,KAAK,GAAGK,GAAG,CAACC,OAAO,GAAG,8BAA8B,CAAC;IAC5E,CAAC,SAAS;MACRZ,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM6B,YAAY,GAAG,MAAOD,EAAU,IAAK;IACzC,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MACxE;IACF;IAEA,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0CyB,EAAE,EAAE,EAAE;QAC3EJ,MAAM,EAAE,QAAQ;QAChBpB,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEAxB,eAAe,CAACD,YAAY,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKA,EAAE,CAAC,CAAC;MACtDF,KAAK,CAAC,mCAAmC,CAAC;IAC5C,CAAC,CAAC,OAAOf,GAAG,EAAE;MACZe,KAAK,CAACf,GAAG,YAAYL,KAAK,GAAGK,GAAG,CAACC,OAAO,GAAG,8BAA8B,CAAC;IAC5E;EACF,CAAC;EAED,MAAMsB,UAAU,GAAIC,WAAwB,IAAK;IAC/CrC,sBAAsB,CAACqC,WAAW,CAAC;IACnCvC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMwC,UAAU,GAAID,WAAwB,IAAK;IAC/C;IACAL,MAAM,CAACO,IAAI,CAAC,iBAAiBF,WAAW,CAACP,EAAE,EAAE,EAAE,QAAQ,CAAC;EAC1D,CAAC;EAED,MAAMU,iBAAiB,GAAGA,CAAA,KAAM;IAC9B5C,eAAe,CAAC,KAAK,CAAC;IACtBE,gBAAgB,CAAC,KAAK,CAAC;IACvBE,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMyC,oBAAoB,GAAG1D,YAAY,CAACmD,MAAM,CAACG,WAAW,IAAI;IAC9D,MAAMK,aAAa,GAAGL,WAAW,CAACM,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxD,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC,IACnEP,WAAW,CAACS,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxD,UAAU,CAACuD,WAAW,CAAC,CAAC,CAAC;IAC7F,MAAMG,YAAY,GAAG,CAACxD,WAAW,IAAI8C,WAAW,CAACW,KAAK,KAAKzD,WAAW;IACtE,MAAM0D,cAAc,GAAG,CAACxD,aAAa,IAAI4C,WAAW,CAACa,OAAO,KAAKzD,aAAa;IAE9E,OAAOiD,aAAa,IAAIK,YAAY,IAAIE,cAAc;EACxD,CAAC,CAAC;EAEF,MAAME,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACrE,YAAY,CAACsE,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACa,KAAK,CAAC,CAAC,CAAC,CAACd,MAAM,CAACoB,OAAO,CAAC;EACjF,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIH,GAAG,CAACrE,YAAY,CAACsE,GAAG,CAAClB,CAAC,IAAIA,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAAChB,MAAM,CAACoB,OAAO,CAAC;EAEtF,IAAIrE,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK4E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD7E,OAAA;QAAK4E,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACEjF,OAAA;IAAK4E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7E,OAAA;MAAK4E,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD7E,OAAA;QAAA6E,QAAA,gBACE7E,OAAA;UAAI4E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EjF,OAAA;UAAG4E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC,eACNjF,OAAA;QAAK4E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7E,OAAA;UACEkF,OAAO,EAAEA,CAAA,KAAMlE,eAAe,CAAC,IAAI,CAAE;UACrC4D,SAAS,EAAC,2FAA2F;UAAAC,QAAA,gBAErG7E,OAAA,CAACV,QAAQ;YAACsF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCjF,OAAA;YAAA6E,QAAA,EAAM;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACTjF,OAAA;UAAQ4E,SAAS,EAAC,6FAA6F;UAAAC,QAAA,gBAC7G7E,OAAA,CAACJ,mBAAmB;YAACgF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CjF,OAAA;YAAA6E,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA;MAAK4E,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eACvD7E,OAAA;QAAK4E,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD7E,OAAA;UAAK4E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB7E,OAAA,CAACN,mBAAmB;YAACkF,SAAS,EAAC;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/EjF,OAAA;YACEmF,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wBAAwB;YACpCC,KAAK,EAAE5E,UAAW;YAClB6E,QAAQ,EAAGC,CAAC,IAAK7E,aAAa,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAAoH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjF,OAAA;UACEqF,KAAK,EAAE1E,WAAY;UACnB2E,QAAQ,EAAGC,CAAC,IAAK3E,cAAc,CAAC2E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDT,SAAS,EAAC,uGAAuG;UAAAC,QAAA,gBAEjH7E,OAAA;YAAQqF,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnCV,YAAY,CAACE,GAAG,CAACL,KAAK,iBACrBpE,OAAA;YAAoBqF,KAAK,EAAEjB,KAAM;YAAAS,QAAA,EAAET;UAAK,GAA3BA,KAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA+B,CAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTjF,OAAA;UACEqF,KAAK,EAAExE,aAAc;UACrByE,QAAQ,EAAGC,CAAC,IAAKzE,gBAAgB,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClDT,SAAS,EAAC,uGAAuG;UAAAC,QAAA,gBAEjH7E,OAAA;YAAQqF,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACtCN,eAAe,CAACF,GAAG,CAACH,OAAO,iBAC1BtE,OAAA;YAAsBqF,KAAK,EAAEf,OAAQ;YAAAO,QAAA,EAAEP;UAAO,GAAjCA,OAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTjF,OAAA;UAAQ4E,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAC9G7E,OAAA,CAACL,UAAU;YAACiF,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClCjF,OAAA;YAAA6E,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1E,KAAK,iBACJP,OAAA;MAAK4E,SAAS,EAAC,mEAAmE;MAAAC,QAAA,EAC/EtE;IAAK;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDjF,OAAA;MAAK4E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD7E,OAAA;QAAK4E,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvD7E,OAAA;UAAK4E,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAE1E,YAAY,CAACsF;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7EjF,OAAA;UAAK4E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNjF,OAAA;QAAK4E,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvD7E,OAAA;UAAK4E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC/C1E,YAAY,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACmC,MAAM,CAAC,CAACD;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNjF,OAAA;UAAK4E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACNjF,OAAA;QAAK4E,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvD7E,OAAA;UAAK4E,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChD1E,YAAY,CAACmD,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACmC,MAAM,CAAC,CAACD;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNjF,OAAA;UAAK4E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACNjF,OAAA;QAAK4E,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvD7E,OAAA;UAAK4E,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAEF,eAAe,CAACc;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFjF,OAAA;UAAK4E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA;MAAK4E,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE7E,OAAA;QAAK4E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B7E,OAAA;UAAO4E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpD7E,OAAA;YAAO4E,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3B7E,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBAAI4E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRjF,OAAA;YAAO4E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDhB,oBAAoB,CAACY,GAAG,CAAEhB,WAAW,iBACpCzD,OAAA;cAAyB4E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBACnD7E,OAAA;gBAAI4E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC7E,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAK4E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/CpB,WAAW,CAACM;kBAAK;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACNjF,OAAA;oBAAK4E,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EACrDpB,WAAW,CAACS;kBAAW;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DpB,WAAW,CAACa;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC7E,OAAA;kBAAM4E,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,EACjGpB,WAAW,CAACW;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9D,IAAIc,IAAI,CAAClC,WAAW,CAACmC,QAAQ,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzC7E,OAAA;kBAAM4E,SAAS,EAAE,4DACfnB,WAAW,CAACiC,MAAM,GACd,6BAA6B,GAC7B,yBAAyB,EAC5B;kBAAAb,QAAA,EACApB,WAAW,CAACiC,MAAM,GAAG,MAAM,GAAG;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLjF,OAAA;gBAAI4E,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,eAC7D7E,OAAA;kBAAK4E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B7E,OAAA;oBACEkF,OAAO,EAAEA,CAAA,KAAMxB,UAAU,CAACD,WAAW,CAAE;oBACvCmB,SAAS,EAAC,mCAAmC;oBAC7Cb,KAAK,EAAC,kBAAkB;oBAAAc,QAAA,eAExB7E,OAAA,CAACP,OAAO;sBAACmF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACTjF,OAAA;oBACEkF,OAAO,EAAEA,CAAA,KAAM1B,UAAU,CAACC,WAAW,CAAE;oBACvCmB,SAAS,EAAC,qCAAqC;oBAC/Cb,KAAK,EAAC,kBAAkB;oBAAAc,QAAA,eAExB7E,OAAA,CAACT,UAAU;sBAACqF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACTjF,OAAA;oBACEkF,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAACM,WAAW,CAACP,EAAE,CAAE;oBAC5C0B,SAAS,EAAC,iCAAiC;oBAC3Cb,KAAK,EAAC,oBAAoB;oBAAAc,QAAA,eAE1B7E,OAAA,CAACR,SAAS;sBAACoF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAvDExB,WAAW,CAACP,EAAE;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwDnB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAELpB,oBAAoB,CAAC4B,MAAM,KAAK,CAAC,iBAChCzF,OAAA;QAAK4E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC7E,OAAA;UAAK4E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLlE,YAAY,iBACXf,OAAA,CAACF,KAAK;MACJ4F,MAAM,EAAE3E,YAAa;MACrB+E,OAAO,EAAElC,iBAAkB;MAC3BG,KAAK,EAAC,qBAAqB;MAAAc,QAAA,gBAE3B7E,OAAA,CAACH,eAAe;QACd4D,WAAW,EAAE,IAAK;QAClBsC,QAAQ,EAAE5D,uBAAwB;QAClC6D,QAAQ,EAAEpC;MAAkB;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EACD5D,YAAY,iBACXrB,OAAA;QAAK4E,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvF7E,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7E,OAAA;YAAK4E,SAAS,EAAC;UAAsE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5FjF,OAAA;YAAG4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACR,EAGAhE,aAAa,IAAIE,mBAAmB,iBACnCnB,OAAA,CAACF,KAAK;MACJ4F,MAAM,EAAEzE,aAAc;MACtB6E,OAAO,EAAElC,iBAAkB;MAC3BG,KAAK,EAAC,kBAAkB;MACxBkC,IAAI,EAAC,OAAO;MAAApB,QAAA,gBAEZ7E,OAAA,CAACH,eAAe;QACd4D,WAAW,EAAEtC,mBAAoB;QACjC4E,QAAQ,EAAE9C,uBAAwB;QAClC+C,QAAQ,EAAEpC;MAAkB;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,EACD5D,YAAY,iBACXrB,OAAA;QAAK4E,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvF7E,OAAA;UAAK4E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7E,OAAA;YAAK4E,SAAS,EAAC;UAAsE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5FjF,OAAA;YAAG4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/E,EAAA,CA3ZID,kBAA4B;AAAAiG,EAAA,GAA5BjG,kBAA4B;AA6ZlC,eAAeA,kBAAkB;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}