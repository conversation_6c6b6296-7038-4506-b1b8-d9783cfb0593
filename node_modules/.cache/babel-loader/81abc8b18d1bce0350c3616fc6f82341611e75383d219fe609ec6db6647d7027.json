{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PlusIcon, PencilIcon, TrashIcon, EyeIcon, MagnifyingGlassIcon, FunnelIcon, DocumentArrowUpIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScholarshipManager = () => {\n  _s();\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterLevel, setFilterLevel] = useState('');\n  const [filterCountry, setFilterCountry] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedScholarship, setSelectedScholarship] = useState(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  useEffect(() => {\n    fetchScholarships();\n  }, []);\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/scholarships', {\n        credentials: 'include'\n      });\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      setScholarships(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDelete = async id => {\n    if (!window.confirm('Are you sure you want to delete this scholarship?')) {\n      return;\n    }\n    try {\n      const response = await fetch(`/api/scholarships/${id}`, {\n        method: 'DELETE',\n        credentials: 'include'\n      });\n      if (!response.ok) {\n        throw new Error('Failed to delete scholarship');\n      }\n      setScholarships(scholarships.filter(s => s.id !== id));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to delete scholarship');\n    }\n  };\n  const filteredScholarships = scholarships.filter(scholarship => {\n    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) || scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesLevel = !filterLevel || scholarship.level === filterLevel;\n    const matchesCountry = !filterCountry || scholarship.country === filterCountry;\n    return matchesSearch && matchesLevel && matchesCountry;\n  });\n  const uniqueLevels = [...new Set(scholarships.map(s => s.level))].filter(Boolean);\n  const uniqueCountries = [...new Set(scholarships.map(s => s.country))].filter(Boolean);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Scholarship Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage scholarships, add new ones, and update existing entries\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddModal(true),\n          className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add Scholarship\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(DocumentArrowUpIcon, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Bulk Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow-sm border\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n            className: \"h-5 w-5 absolute left-3 top-3 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search scholarships...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterLevel,\n          onChange: e => setFilterLevel(e.target.value),\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Levels\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), uniqueLevels.map(level => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: level,\n            children: level\n          }, level, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: filterCountry,\n          onChange: e => setFilterCountry(e.target.value),\n          className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"All Countries\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), uniqueCountries.map(country => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: country,\n            children: country\n          }, country, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"More Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-blue-600\",\n          children: scholarships.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Total Scholarships\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-green-600\",\n          children: scholarships.filter(s => s.is_open).length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Open Applications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-orange-600\",\n          children: scholarships.filter(s => !s.is_open).length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Closed Applications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-4 rounded-lg shadow-sm border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-purple-600\",\n          children: uniqueCountries.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600\",\n          children: \"Countries\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Scholarship\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Country\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Deadline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredScholarships.map(scholarship => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: scholarship.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500 truncate max-w-xs\",\n                    children: scholarship.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: scholarship.country\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                  children: scholarship.level\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                children: new Date(scholarship.deadline).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${scholarship.is_open ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: scholarship.is_open ? 'Open' : 'Closed'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-blue-600 hover:text-blue-900\",\n                    children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-green-600 hover:text-green-900\",\n                    children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(scholarship.id),\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, scholarship.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), filteredScholarships.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-500\",\n          children: \"No scholarships found matching your criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n_s(ScholarshipManager, \"lxXZ1eFyCYhFooAbPo0IBLInTdQ=\");\n_c = ScholarshipManager;\nexport default ScholarshipManager;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "MagnifyingGlassIcon", "FunnelIcon", "DocumentArrowUpIcon", "jsxDEV", "_jsxDEV", "ScholarshipManager", "_s", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "filterLevel", "setFilterLevel", "filterCountry", "setFilterCountry", "showAddModal", "setShowAddModal", "showEditModal", "setShowEditModal", "selectedScholarship", "setSelectedScholarship", "isSubmitting", "setIsSubmitting", "fetchScholarships", "response", "fetch", "credentials", "ok", "Error", "data", "json", "err", "message", "handleDelete", "id", "window", "confirm", "method", "filter", "s", "filteredScholarships", "scholarship", "matchesSearch", "title", "toLowerCase", "includes", "description", "matchesLevel", "level", "matchesCountry", "country", "uniqueLevels", "Set", "map", "Boolean", "uniqueCountries", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "value", "onChange", "e", "target", "length", "is_open", "Date", "deadline", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  DocumentArrowUpIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport ScholarshipForm from '../../admin/components/ScholarshipForm';\nimport Modal from '../../admin/components/Modal';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  country: string;\n  level: string;\n  deadline: string;\n  amount?: string;\n  isOpen: boolean;\n  thumbnail?: string;\n  coverage?: string;\n  financialBenefitsSummary?: string;\n  eligibilitySummary?: string;\n  scholarshipLink?: string;\n  youtubeLink?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst ScholarshipManager: React.FC = () => {\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterLevel, setFilterLevel] = useState('');\n  const [filterCountry, setFilterCountry] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedScholarship, setSelectedScholarship] = useState<Scholarship | null>(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  useEffect(() => {\n    fetchScholarships();\n  }, []);\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/scholarships', {\n        credentials: 'include'\n      });\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      \n      const data = await response.json();\n      setScholarships(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    if (!window.confirm('Are you sure you want to delete this scholarship?')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/scholarships/${id}`, {\n        method: 'DELETE',\n        credentials: 'include'\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to delete scholarship');\n      }\n\n      setScholarships(scholarships.filter(s => s.id !== id));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to delete scholarship');\n    }\n  };\n\n  const filteredScholarships = scholarships.filter(scholarship => {\n    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesLevel = !filterLevel || scholarship.level === filterLevel;\n    const matchesCountry = !filterCountry || scholarship.country === filterCountry;\n    \n    return matchesSearch && matchesLevel && matchesCountry;\n  });\n\n  const uniqueLevels = [...new Set(scholarships.map(s => s.level))].filter(Boolean);\n  const uniqueCountries = [...new Set(scholarships.map(s => s.country))].filter(Boolean);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Scholarship Management</h1>\n          <p className=\"text-gray-600\">Manage scholarships, add new ones, and update existing entries</p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={() => setShowAddModal(true)}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\n          >\n            <PlusIcon className=\"h-5 w-5\" />\n            <span>Add Scholarship</span>\n          </button>\n          <button className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2\">\n            <DocumentArrowUpIcon className=\"h-5 w-5\" />\n            <span>Bulk Import</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"relative\">\n            <MagnifyingGlassIcon className=\"h-5 w-5 absolute left-3 top-3 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search scholarships...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <select\n            value={filterLevel}\n            onChange={(e) => setFilterLevel(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">All Levels</option>\n            {uniqueLevels.map(level => (\n              <option key={level} value={level}>{level}</option>\n            ))}\n          </select>\n          <select\n            value={filterCountry}\n            onChange={(e) => setFilterCountry(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">All Countries</option>\n            {uniqueCountries.map(country => (\n              <option key={country} value={country}>{country}</option>\n            ))}\n          </select>\n          <button className=\"bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2\">\n            <FunnelIcon className=\"h-5 w-5\" />\n            <span>More Filters</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n          {error}\n        </div>\n      )}\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-blue-600\">{scholarships.length}</div>\n          <div className=\"text-sm text-gray-600\">Total Scholarships</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {scholarships.filter(s => s.is_open).length}\n          </div>\n          <div className=\"text-sm text-gray-600\">Open Applications</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-orange-600\">\n            {scholarships.filter(s => !s.is_open).length}\n          </div>\n          <div className=\"text-sm text-gray-600\">Closed Applications</div>\n        </div>\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border\">\n          <div className=\"text-2xl font-bold text-purple-600\">{uniqueCountries.length}</div>\n          <div className=\"text-sm text-gray-600\">Countries</div>\n        </div>\n      </div>\n\n      {/* Scholarships Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Scholarship\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Country\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Level\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Deadline\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredScholarships.map((scholarship) => (\n                <tr key={scholarship.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {scholarship.title}\n                      </div>\n                      <div className=\"text-sm text-gray-500 truncate max-w-xs\">\n                        {scholarship.description}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {scholarship.country}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n                      {scholarship.level}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {new Date(scholarship.deadline).toLocaleDateString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                      scholarship.is_open \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {scholarship.is_open ? 'Open' : 'Closed'}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-900\">\n                        <EyeIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-900\">\n                        <PencilIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button \n                        onClick={() => handleDelete(scholarship.id)}\n                        className=\"text-red-600 hover:text-red-900\"\n                      >\n                        <TrashIcon className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n        \n        {filteredScholarships.length === 0 && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-500\">No scholarships found matching your criteria.</div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ScholarshipManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,UAAU,EACVC,SAAS,EACTC,OAAO,EACPC,mBAAmB,EACnBC,UAAU,EACVC,mBAAmB,QAEd,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBrC,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdgC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,mBAAmB,EAAE;QAChDC,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC1B,eAAe,CAACyB,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZvB,QAAQ,CAACuB,GAAG,YAAYH,KAAK,GAAGG,GAAG,CAACC,OAAO,GAAG,mBAAmB,CAAC;IACpE,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MACxE;IACF;IAEA,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqBS,EAAE,EAAE,EAAE;QACtDG,MAAM,EAAE,QAAQ;QAChBX,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEAxB,eAAe,CAACD,YAAY,CAACmC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKA,EAAE,CAAC,CAAC;IACxD,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZvB,QAAQ,CAACuB,GAAG,YAAYH,KAAK,GAAGG,GAAG,CAACC,OAAO,GAAG,8BAA8B,CAAC;IAC/E;EACF,CAAC;EAED,MAAMQ,oBAAoB,GAAGrC,YAAY,CAACmC,MAAM,CAACG,WAAW,IAAI;IAC9D,MAAMC,aAAa,GAAGD,WAAW,CAACE,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpC,UAAU,CAACmC,WAAW,CAAC,CAAC,CAAC,IACnEH,WAAW,CAACK,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpC,UAAU,CAACmC,WAAW,CAAC,CAAC,CAAC;IAC7F,MAAMG,YAAY,GAAG,CAACpC,WAAW,IAAI8B,WAAW,CAACO,KAAK,KAAKrC,WAAW;IACtE,MAAMsC,cAAc,GAAG,CAACpC,aAAa,IAAI4B,WAAW,CAACS,OAAO,KAAKrC,aAAa;IAE9E,OAAO6B,aAAa,IAAIK,YAAY,IAAIE,cAAc;EACxD,CAAC,CAAC;EAEF,MAAME,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACjD,YAAY,CAACkD,GAAG,CAACd,CAAC,IAAIA,CAAC,CAACS,KAAK,CAAC,CAAC,CAAC,CAACV,MAAM,CAACgB,OAAO,CAAC;EACjF,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIH,GAAG,CAACjD,YAAY,CAACkD,GAAG,CAACd,CAAC,IAAIA,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACZ,MAAM,CAACgB,OAAO,CAAC;EAEtF,IAAIjD,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKwD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDzD,OAAA;QAAKwD,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE7D,OAAA;IAAKwD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzD,OAAA;MAAKwD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDzD,OAAA;QAAAyD,QAAA,gBACEzD,OAAA;UAAIwD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5E7D,OAAA;UAAGwD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC,eACN7D,OAAA;QAAKwD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzD,OAAA;UACE8D,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAAC,IAAI,CAAE;UACrCwC,SAAS,EAAC,2FAA2F;UAAAC,QAAA,gBAErGzD,OAAA,CAACR,QAAQ;YAACgE,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChC7D,OAAA;YAAAyD,QAAA,EAAM;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACT7D,OAAA;UAAQwD,SAAS,EAAC,6FAA6F;UAAAC,QAAA,gBAC7GzD,OAAA,CAACF,mBAAmB;YAAC0D,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3C7D,OAAA;YAAAyD,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eACvDzD,OAAA;QAAKwD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDzD,OAAA;UAAKwD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBzD,OAAA,CAACJ,mBAAmB;YAAC4D,SAAS,EAAC;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/E7D,OAAA;YACE+D,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,wBAAwB;YACpCC,KAAK,EAAExD,UAAW;YAClByD,QAAQ,EAAGC,CAAC,IAAKzD,aAAa,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/CT,SAAS,EAAC;UAAoH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7D,OAAA;UACEiE,KAAK,EAAEtD,WAAY;UACnBuD,QAAQ,EAAGC,CAAC,IAAKvD,cAAc,CAACuD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDT,SAAS,EAAC,uGAAuG;UAAAC,QAAA,gBAEjHzD,OAAA;YAAQiE,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnCV,YAAY,CAACE,GAAG,CAACL,KAAK,iBACrBhD,OAAA;YAAoBiE,KAAK,EAAEjB,KAAM;YAAAS,QAAA,EAAET;UAAK,GAA3BA,KAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA+B,CAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT7D,OAAA;UACEiE,KAAK,EAAEpD,aAAc;UACrBqD,QAAQ,EAAGC,CAAC,IAAKrD,gBAAgB,CAACqD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAClDT,SAAS,EAAC,uGAAuG;UAAAC,QAAA,gBAEjHzD,OAAA;YAAQiE,KAAK,EAAC,EAAE;YAAAR,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACtCN,eAAe,CAACF,GAAG,CAACH,OAAO,iBAC1BlD,OAAA;YAAsBiE,KAAK,EAAEf,OAAQ;YAAAO,QAAA,EAAEP;UAAO,GAAjCA,OAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT7D,OAAA;UAAQwD,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAC9GzD,OAAA,CAACH,UAAU;YAAC2D,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClC7D,OAAA;YAAAyD,QAAA,EAAM;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtD,KAAK,iBACJP,OAAA;MAAKwD,SAAS,EAAC,mEAAmE;MAAAC,QAAA,EAC/ElD;IAAK;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD7D,OAAA;MAAKwD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzD,OAAA;QAAKwD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDzD,OAAA;UAAKwD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAEtD,YAAY,CAACkE;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7E7D,OAAA;UAAKwD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACN7D,OAAA;QAAKwD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDzD,OAAA;UAAKwD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC/CtD,YAAY,CAACmC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC+B,OAAO,CAAC,CAACD;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACN7D,OAAA;UAAKwD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACN7D,OAAA;QAAKwD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDzD,OAAA;UAAKwD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDtD,YAAY,CAACmC,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC+B,OAAO,CAAC,CAACD;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN7D,OAAA;UAAKwD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACN7D,OAAA;QAAKwD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,gBACvDzD,OAAA;UAAKwD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAEF,eAAe,CAACc;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClF7D,OAAA;UAAKwD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEzD,OAAA;QAAKwD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzD,OAAA;UAAOwD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDzD,OAAA;YAAOwD,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BzD,OAAA;cAAAyD,QAAA,gBACEzD,OAAA;gBAAIwD,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR7D,OAAA;YAAOwD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDjB,oBAAoB,CAACa,GAAG,CAAEZ,WAAW,iBACpCzC,OAAA;cAAyBwD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBACnDzD,OAAA;gBAAIwD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCzD,OAAA;kBAAAyD,QAAA,gBACEzD,OAAA;oBAAKwD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/ChB,WAAW,CAACE;kBAAK;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACN7D,OAAA;oBAAKwD,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EACrDhB,WAAW,CAACK;kBAAW;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DhB,WAAW,CAACS;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCzD,OAAA;kBAAMwD,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,EACjGhB,WAAW,CAACO;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9D,IAAIc,IAAI,CAAC9B,WAAW,CAAC+B,QAAQ,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCzD,OAAA;kBAAMwD,SAAS,EAAE,4DACff,WAAW,CAAC6B,OAAO,GACf,6BAA6B,GAC7B,yBAAyB,EAC5B;kBAAAb,QAAA,EACAhB,WAAW,CAAC6B,OAAO,GAAG,MAAM,GAAG;gBAAQ;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7D,OAAA;gBAAIwD,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,eAC7DzD,OAAA;kBAAKwD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BzD,OAAA;oBAAQwD,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,eACnDzD,OAAA,CAACL,OAAO;sBAAC6D,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACT7D,OAAA;oBAAQwD,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eACrDzD,OAAA,CAACP,UAAU;sBAAC+D,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACT7D,OAAA;oBACE8D,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACQ,WAAW,CAACP,EAAE,CAAE;oBAC5CsB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,eAE3CzD,OAAA,CAACN,SAAS;sBAAC8D,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA9CEpB,WAAW,CAACP,EAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+CnB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAELrB,oBAAoB,CAAC6B,MAAM,KAAK,CAAC,iBAChCrE,OAAA;QAAKwD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCzD,OAAA;UAAKwD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CApQID,kBAA4B;AAAAyE,EAAA,GAA5BzE,kBAA4B;AAsQlC,eAAeA,kBAAkB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}