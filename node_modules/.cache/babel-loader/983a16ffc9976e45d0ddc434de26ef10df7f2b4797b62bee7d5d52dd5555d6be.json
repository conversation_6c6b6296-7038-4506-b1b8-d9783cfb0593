{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';\nimport { Button, Table, Space, Modal, Form, Input, Select, Switch, DatePicker, Tag, message } from 'antd';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst OpportunityManager = () => {\n  _s();\n  const [opportunities, setOpportunities] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingOpportunity, setEditingOpportunity] = useState(null);\n  const [form] = Form.useForm();\n  useEffect(() => {\n    fetchOpportunities();\n  }, []);\n  const fetchOpportunities = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/opportunities?limit=100', {\n        credentials: 'include'\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setOpportunities(data.data.opportunities || []);\n      } else {\n        message.error('Failed to fetch opportunities');\n      }\n    } catch (error) {\n      console.error('Error fetching opportunities:', error);\n      message.error('Error fetching opportunities');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreate = () => {\n    setEditingOpportunity(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = opportunity => {\n    var _opportunity$tags;\n    setEditingOpportunity(opportunity);\n    form.setFieldsValue({\n      ...opportunity,\n      deadline: opportunity.deadline ? dayjs(opportunity.deadline) : null,\n      startDate: opportunity.startDate ? dayjs(opportunity.startDate) : null,\n      endDate: opportunity.endDate ? dayjs(opportunity.endDate) : null,\n      tags: ((_opportunity$tags = opportunity.tags) === null || _opportunity$tags === void 0 ? void 0 : _opportunity$tags.join(', ')) || ''\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    Modal.confirm({\n      title: 'Delete Opportunity',\n      content: 'Are you sure you want to delete this opportunity? This action cannot be undone.',\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          const response = await fetch(`/api/opportunities/${id}`, {\n            method: 'DELETE',\n            credentials: 'include'\n          });\n          if (response.ok) {\n            message.success('Opportunity deleted successfully');\n            fetchOpportunities();\n          } else {\n            message.error('Failed to delete opportunity');\n          }\n        } catch (error) {\n          console.error('Error deleting opportunity:', error);\n          message.error('Error deleting opportunity');\n        }\n      }\n    });\n  };\n  const handleSubmit = async values => {\n    try {\n      const opportunityData = {\n        ...values,\n        deadline: values.deadline ? values.deadline.toISOString() : null,\n        startDate: values.startDate ? values.startDate.toISOString() : null,\n        endDate: values.endDate ? values.endDate.toISOString() : null,\n        tags: values.tags ? values.tags.split(',').map(tag => tag.trim()).filter(Boolean) : []\n      };\n      const url = editingOpportunity ? `/api/opportunities/${editingOpportunity.id}` : '/api/opportunities';\n      const method = editingOpportunity ? 'PUT' : 'POST';\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(opportunityData)\n      });\n      if (response.ok) {\n        message.success(`Opportunity ${editingOpportunity ? 'updated' : 'created'} successfully`);\n        setModalVisible(false);\n        fetchOpportunities();\n      } else {\n        const errorData = await response.json();\n        message.error(errorData.message || `Failed to ${editingOpportunity ? 'update' : 'create'} opportunity`);\n      }\n    } catch (error) {\n      console.error('Error saving opportunity:', error);\n      message.error('Error saving opportunity');\n    }\n  };\n  const getTypeColor = type => {\n    const colors = {\n      internship: 'blue',\n      training: 'green',\n      conference: 'purple',\n      workshop: 'orange',\n      competition: 'red'\n    };\n    return colors[type] || 'default';\n  };\n  const getTypeLabel = type => {\n    const labels = {\n      internship: 'Internship',\n      training: 'Training',\n      conference: 'Conference',\n      workshop: 'Workshop',\n      competition: 'Competition'\n    };\n    return labels[type] || type;\n  };\n  const isExpired = deadline => {\n    return new Date(deadline) < new Date();\n  };\n  const columns = [{\n    title: 'Title',\n    dataIndex: 'title',\n    key: 'title',\n    width: '20%',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: record.organization\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Type',\n    dataIndex: 'type',\n    key: 'type',\n    width: '12%',\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getTypeColor(type),\n      children: getTypeLabel(type)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Location',\n    dataIndex: 'location',\n    key: 'location',\n    width: '15%',\n    render: (location, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: location\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), record.isRemote && /*#__PURE__*/_jsxDEV(Tag, {\n        className: \"text-xs\",\n        color: \"green\",\n        children: \"Remote\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 31\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Deadline',\n    dataIndex: 'deadline',\n    key: 'deadline',\n    width: '12%',\n    render: deadline => {\n      const expired = isExpired(deadline);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: expired ? 'text-red-500' : '',\n        children: [new Date(deadline).toLocaleDateString(), expired && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs\",\n          children: \"Expired\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: 'Status',\n    dataIndex: 'isActive',\n    key: 'isActive',\n    width: '10%',\n    render: (isActive, record) => {\n      const expired = isExpired(record.deadline);\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: isActive && !expired ? 'green' : 'red',\n        children: expired ? 'Expired' : isActive ? 'Active' : 'Inactive'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: 'Tags',\n    dataIndex: 'tags',\n    key: 'tags',\n    width: '15%',\n    render: tags => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [tags === null || tags === void 0 ? void 0 : tags.slice(0, 2).map((tag, index) => /*#__PURE__*/_jsxDEV(Tag, {\n        className: \"text-xs\",\n        children: tag\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 13\n      }, this)), (tags === null || tags === void 0 ? void 0 : tags.length) > 2 && /*#__PURE__*/_jsxDEV(Tag, {\n        className: \"text-xs\",\n        children: [\"+\", tags.length - 2]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Created',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: '10%',\n    render: date => new Date(date).toLocaleDateString()\n  }, {\n    title: 'Actions',\n    key: 'actions',\n    width: '6%',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 19\n        }, this),\n        onClick: () => window.open(`/opportunities/${record.id}`, '_blank'),\n        title: \"View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        title: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        title: \"Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Opportunity Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage internships, training, conferences, and competitions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 17\n        }, this),\n        onClick: handleCreate,\n        size: \"large\",\n        children: \"Create Opportunity\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: opportunities,\n      rowKey: \"id\",\n      loading: loading,\n      pagination: {\n        pageSize: 10,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: total => `Total ${total} opportunities`\n      },\n      scroll: {\n        x: 1400\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingOpportunity ? 'Edit Opportunity' : 'Create Opportunity',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      footer: null,\n      width: 900,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          isActive: true,\n          isRemote: false,\n          type: 'internship'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"title\",\n            label: \"Title\",\n            rules: [{\n              required: true,\n              message: 'Please enter the title'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"Enter opportunity title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"type\",\n            label: \"Type\",\n            rules: [{\n              required: true,\n              message: 'Please select a type'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select type\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"internship\",\n                children: \"Internship\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"training\",\n                children: \"Training\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"conference\",\n                children: \"Conference\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"workshop\",\n                children: \"Workshop\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"competition\",\n                children: \"Competition\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"Description\",\n          rules: [{\n            required: true,\n            message: 'Please enter the description'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"Detailed description of the opportunity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"organization\",\n            label: \"Organization\",\n            rules: [{\n              required: true,\n              message: 'Please enter the organization'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"Organization name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"location\",\n            label: \"Location\",\n            rules: [{\n              required: true,\n              message: 'Please enter the location'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"City, Country\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"deadline\",\n            label: \"Application Deadline\",\n            rules: [{\n              required: true,\n              message: 'Please select the deadline'\n            }],\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              className: \"w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"startDate\",\n            label: \"Start Date\",\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              className: \"w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"endDate\",\n            label: \"End Date\",\n            children: /*#__PURE__*/_jsxDEV(DatePicker, {\n              className: \"w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"applicationLink\",\n            label: \"Application Link\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"https://example.com/apply\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"website\",\n            label: \"Website\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"https://example.com\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"contactEmail\",\n            label: \"Contact Email\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"<EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"thumbnail\",\n            label: \"Thumbnail URL\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"https://example.com/image.jpg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"requirements\",\n          label: \"Requirements\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"Requirements and qualifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"benefits\",\n          label: \"Benefits\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"Benefits and compensation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"tags\",\n          label: \"Tags\",\n          help: \"Comma-separated tags\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"internship, tech, remote\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"isRemote\",\n            label: \"Remote Work\",\n            valuePropName: \"checked\",\n            children: /*#__PURE__*/_jsxDEV(Switch, {\n              checkedChildren: \"Remote\",\n              unCheckedChildren: \"On-site\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"isActive\",\n            label: \"Status\",\n            valuePropName: \"checked\",\n            children: /*#__PURE__*/_jsxDEV(Switch, {\n              checkedChildren: \"Active\",\n              unCheckedChildren: \"Inactive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-2 pt-4 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setModalVisible(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: [editingOpportunity ? 'Update' : 'Create', \" Opportunity\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(OpportunityManager, \"bZBo+k8/DFSKSvHnw2IeJlOgwuk=\", false, function () {\n  return [Form.useForm];\n});\n_c = OpportunityManager;\nexport default OpportunityManager;\nvar _c;\n$RefreshReg$(_c, \"OpportunityManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "<PERSON><PERSON>", "Table", "Space", "Modal", "Form", "Input", "Select", "Switch", "DatePicker", "Tag", "message", "dayjs", "jsxDEV", "_jsxDEV", "TextArea", "Option", "OpportunityManager", "_s", "opportunities", "setOpportunities", "loading", "setLoading", "modalVisible", "setModalVisible", "editingOpportunity", "setEditingOpportunity", "form", "useForm", "fetchOpportunities", "response", "fetch", "credentials", "ok", "data", "json", "error", "console", "handleCreate", "resetFields", "handleEdit", "opportunity", "_opportunity$tags", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadline", "startDate", "endDate", "tags", "join", "handleDelete", "id", "confirm", "title", "content", "okText", "okType", "cancelText", "onOk", "method", "success", "handleSubmit", "values", "opportunityData", "toISOString", "split", "map", "tag", "trim", "filter", "Boolean", "url", "headers", "body", "JSON", "stringify", "errorData", "getTypeColor", "type", "colors", "internship", "training", "conference", "workshop", "competition", "getTypeLabel", "labels", "isExpired", "Date", "columns", "dataIndex", "key", "width", "render", "text", "record", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "organization", "color", "location", "isRemote", "expired", "toLocaleDateString", "isActive", "slice", "index", "length", "date", "_", "size", "icon", "onClick", "window", "open", "danger", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "onCancel", "footer", "destroyOnClose", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "value", "rows", "help", "valuePropName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';\nimport { Button, Table, Space, Modal, Form, Input, Select, Switch, DatePicker, Tag, message } from 'antd';\nimport dayjs from 'dayjs';\n\nconst { TextArea } = Input;\nconst { Option } = Select;\n\ninterface Opportunity {\n  id: number;\n  title: string;\n  description: string;\n  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';\n  organization: string;\n  location: string;\n  isRemote: boolean;\n  deadline: string;\n  startDate?: string;\n  endDate?: string;\n  applicationLink?: string;\n  requirements?: string;\n  benefits?: string;\n  thumbnail?: string;\n  isActive: boolean;\n  tags?: string[];\n  contactEmail?: string;\n  website?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst OpportunityManager: React.FC = () => {\n  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingOpportunity, setEditingOpportunity] = useState<Opportunity | null>(null);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchOpportunities();\n  }, []);\n\n  const fetchOpportunities = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/opportunities?limit=100', {\n        credentials: 'include'\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setOpportunities(data.data.opportunities || []);\n      } else {\n        message.error('Failed to fetch opportunities');\n      }\n    } catch (error) {\n      console.error('Error fetching opportunities:', error);\n      message.error('Error fetching opportunities');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreate = () => {\n    setEditingOpportunity(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (opportunity: Opportunity) => {\n    setEditingOpportunity(opportunity);\n    form.setFieldsValue({\n      ...opportunity,\n      deadline: opportunity.deadline ? dayjs(opportunity.deadline) : null,\n      startDate: opportunity.startDate ? dayjs(opportunity.startDate) : null,\n      endDate: opportunity.endDate ? dayjs(opportunity.endDate) : null,\n      tags: opportunity.tags?.join(', ') || ''\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    Modal.confirm({\n      title: 'Delete Opportunity',\n      content: 'Are you sure you want to delete this opportunity? This action cannot be undone.',\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          const response = await fetch(`/api/opportunities/${id}`, {\n            method: 'DELETE',\n            credentials: 'include'\n          });\n          if (response.ok) {\n            message.success('Opportunity deleted successfully');\n            fetchOpportunities();\n          } else {\n            message.error('Failed to delete opportunity');\n          }\n        } catch (error) {\n          console.error('Error deleting opportunity:', error);\n          message.error('Error deleting opportunity');\n        }\n      }\n    });\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      const opportunityData = {\n        ...values,\n        deadline: values.deadline ? values.deadline.toISOString() : null,\n        startDate: values.startDate ? values.startDate.toISOString() : null,\n        endDate: values.endDate ? values.endDate.toISOString() : null,\n        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : []\n      };\n\n      const url = editingOpportunity ? `/api/opportunities/${editingOpportunity.id}` : '/api/opportunities';\n      const method = editingOpportunity ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(opportunityData)\n      });\n\n      if (response.ok) {\n        message.success(`Opportunity ${editingOpportunity ? 'updated' : 'created'} successfully`);\n        setModalVisible(false);\n        fetchOpportunities();\n      } else {\n        const errorData = await response.json();\n        message.error(errorData.message || `Failed to ${editingOpportunity ? 'update' : 'create'} opportunity`);\n      }\n    } catch (error) {\n      console.error('Error saving opportunity:', error);\n      message.error('Error saving opportunity');\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    const colors = {\n      internship: 'blue',\n      training: 'green',\n      conference: 'purple',\n      workshop: 'orange',\n      competition: 'red'\n    };\n    return colors[type as keyof typeof colors] || 'default';\n  };\n\n  const getTypeLabel = (type: string) => {\n    const labels = {\n      internship: 'Internship',\n      training: 'Training',\n      conference: 'Conference',\n      workshop: 'Workshop',\n      competition: 'Competition'\n    };\n    return labels[type as keyof typeof labels] || type;\n  };\n\n  const isExpired = (deadline: string) => {\n    return new Date(deadline) < new Date();\n  };\n\n  const columns = [\n    {\n      title: 'Title',\n      dataIndex: 'title',\n      key: 'title',\n      width: '20%',\n      render: (text: string, record: Opportunity) => (\n        <div>\n          <div className=\"font-medium\">{text}</div>\n          <div className=\"text-sm text-gray-500\">{record.organization}</div>\n        </div>\n      )\n    },\n    {\n      title: 'Type',\n      dataIndex: 'type',\n      key: 'type',\n      width: '12%',\n      render: (type: string) => (\n        <Tag color={getTypeColor(type)}>\n          {getTypeLabel(type)}\n        </Tag>\n      )\n    },\n    {\n      title: 'Location',\n      dataIndex: 'location',\n      key: 'location',\n      width: '15%',\n      render: (location: string, record: Opportunity) => (\n        <div>\n          <div>{location}</div>\n          {record.isRemote && <Tag className=\"text-xs\" color=\"green\">Remote</Tag>}\n        </div>\n      )\n    },\n    {\n      title: 'Deadline',\n      dataIndex: 'deadline',\n      key: 'deadline',\n      width: '12%',\n      render: (deadline: string) => {\n        const expired = isExpired(deadline);\n        return (\n          <div className={expired ? 'text-red-500' : ''}>\n            {new Date(deadline).toLocaleDateString()}\n            {expired && <div className=\"text-xs\">Expired</div>}\n          </div>\n        );\n      }\n    },\n    {\n      title: 'Status',\n      dataIndex: 'isActive',\n      key: 'isActive',\n      width: '10%',\n      render: (isActive: boolean, record: Opportunity) => {\n        const expired = isExpired(record.deadline);\n        return (\n          <Tag color={isActive && !expired ? 'green' : 'red'}>\n            {expired ? 'Expired' : isActive ? 'Active' : 'Inactive'}\n          </Tag>\n        );\n      }\n    },\n    {\n      title: 'Tags',\n      dataIndex: 'tags',\n      key: 'tags',\n      width: '15%',\n      render: (tags: string[]) => (\n        <div>\n          {tags?.slice(0, 2).map((tag, index) => (\n            <Tag key={index} className=\"text-xs\">{tag}</Tag>\n          ))}\n          {tags?.length > 2 && <Tag className=\"text-xs\">+{tags.length - 2}</Tag>}\n        </div>\n      )\n    },\n    {\n      title: 'Created',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: '10%',\n      render: (date: string) => new Date(date).toLocaleDateString()\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      width: '6%',\n      render: (_: any, record: Opportunity) => (\n        <Space size=\"small\">\n          <Button\n            type=\"text\"\n            icon={<EyeOutlined />}\n            onClick={() => window.open(`/opportunities/${record.id}`, '_blank')}\n            title=\"View\"\n          />\n          <Button\n            type=\"text\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n            title=\"Edit\"\n          />\n          <Button\n            type=\"text\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n            title=\"Delete\"\n          />\n        </Space>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Opportunity Management</h1>\n          <p className=\"text-gray-600\">Manage internships, training, conferences, and competitions</p>\n        </div>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleCreate}\n          size=\"large\"\n        >\n          Create Opportunity\n        </Button>\n      </div>\n\n      <Table\n        columns={columns}\n        dataSource={opportunities}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `Total ${total} opportunities`\n        }}\n        scroll={{ x: 1400 }}\n      />\n\n      <Modal\n        title={editingOpportunity ? 'Edit Opportunity' : 'Create Opportunity'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={null}\n        width={900}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            isActive: true,\n            isRemote: false,\n            type: 'internship'\n          }}\n        >\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"title\"\n              label=\"Title\"\n              rules={[{ required: true, message: 'Please enter the title' }]}\n            >\n              <Input placeholder=\"Enter opportunity title\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"type\"\n              label=\"Type\"\n              rules={[{ required: true, message: 'Please select a type' }]}\n            >\n              <Select placeholder=\"Select type\">\n                <Option value=\"internship\">Internship</Option>\n                <Option value=\"training\">Training</Option>\n                <Option value=\"conference\">Conference</Option>\n                <Option value=\"workshop\">Workshop</Option>\n                <Option value=\"competition\">Competition</Option>\n              </Select>\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"description\"\n            label=\"Description\"\n            rules={[{ required: true, message: 'Please enter the description' }]}\n          >\n            <TextArea rows={4} placeholder=\"Detailed description of the opportunity\" />\n          </Form.Item>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"organization\"\n              label=\"Organization\"\n              rules={[{ required: true, message: 'Please enter the organization' }]}\n            >\n              <Input placeholder=\"Organization name\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"location\"\n              label=\"Location\"\n              rules={[{ required: true, message: 'Please enter the location' }]}\n            >\n              <Input placeholder=\"City, Country\" />\n            </Form.Item>\n          </div>\n\n          <div className=\"grid grid-cols-3 gap-4\">\n            <Form.Item\n              name=\"deadline\"\n              label=\"Application Deadline\"\n              rules={[{ required: true, message: 'Please select the deadline' }]}\n            >\n              <DatePicker className=\"w-full\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"startDate\"\n              label=\"Start Date\"\n            >\n              <DatePicker className=\"w-full\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"endDate\"\n              label=\"End Date\"\n            >\n              <DatePicker className=\"w-full\" />\n            </Form.Item>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"applicationLink\"\n              label=\"Application Link\"\n            >\n              <Input placeholder=\"https://example.com/apply\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"website\"\n              label=\"Website\"\n            >\n              <Input placeholder=\"https://example.com\" />\n            </Form.Item>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"contactEmail\"\n              label=\"Contact Email\"\n            >\n              <Input placeholder=\"<EMAIL>\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"thumbnail\"\n              label=\"Thumbnail URL\"\n            >\n              <Input placeholder=\"https://example.com/image.jpg\" />\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"requirements\"\n            label=\"Requirements\"\n          >\n            <TextArea rows={3} placeholder=\"Requirements and qualifications\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"benefits\"\n            label=\"Benefits\"\n          >\n            <TextArea rows={3} placeholder=\"Benefits and compensation\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"tags\"\n            label=\"Tags\"\n            help=\"Comma-separated tags\"\n          >\n            <Input placeholder=\"internship, tech, remote\" />\n          </Form.Item>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"isRemote\"\n              label=\"Remote Work\"\n              valuePropName=\"checked\"\n            >\n              <Switch checkedChildren=\"Remote\" unCheckedChildren=\"On-site\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              valuePropName=\"checked\"\n            >\n              <Switch checkedChildren=\"Active\" unCheckedChildren=\"Inactive\" />\n            </Form.Item>\n          </div>\n\n          <div className=\"flex justify-end space-x-2 pt-4 border-t\">\n            <Button onClick={() => setModalVisible(false)}>\n              Cancel\n            </Button>\n            <Button type=\"primary\" htmlType=\"submit\">\n              {editingOpportunity ? 'Update' : 'Create'} Opportunity\n            </Button>\n          </div>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default OpportunityManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAC3F,SAASC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AACzG,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAS,CAAC,GAAGT,KAAK;AAC1B,MAAM;EAAEU;AAAO,CAAC,GAAGT,MAAM;AAyBzB,MAAMU,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAgB,EAAE,CAAC;EACrE,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAqB,IAAI,CAAC;EACtF,MAAM,CAACgC,IAAI,CAAC,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC;EAE7BhC,SAAS,CAAC,MAAM;IACdiC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,8BAA8B,EAAE;QAC3DC,WAAW,EAAE;MACf,CAAC,CAAC;MACF,IAAIF,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCf,gBAAgB,CAACc,IAAI,CAACA,IAAI,CAACf,aAAa,IAAI,EAAE,CAAC;MACjD,CAAC,MAAM;QACLR,OAAO,CAACyB,KAAK,CAAC,+BAA+B,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDzB,OAAO,CAACyB,KAAK,CAAC,8BAA8B,CAAC;IAC/C,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzBZ,qBAAqB,CAAC,IAAI,CAAC;IAC3BC,IAAI,CAACY,WAAW,CAAC,CAAC;IAClBf,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgB,UAAU,GAAIC,WAAwB,IAAK;IAAA,IAAAC,iBAAA;IAC/ChB,qBAAqB,CAACe,WAAW,CAAC;IAClCd,IAAI,CAACgB,cAAc,CAAC;MAClB,GAAGF,WAAW;MACdG,QAAQ,EAAEH,WAAW,CAACG,QAAQ,GAAGhC,KAAK,CAAC6B,WAAW,CAACG,QAAQ,CAAC,GAAG,IAAI;MACnEC,SAAS,EAAEJ,WAAW,CAACI,SAAS,GAAGjC,KAAK,CAAC6B,WAAW,CAACI,SAAS,CAAC,GAAG,IAAI;MACtEC,OAAO,EAAEL,WAAW,CAACK,OAAO,GAAGlC,KAAK,CAAC6B,WAAW,CAACK,OAAO,CAAC,GAAG,IAAI;MAChEC,IAAI,EAAE,EAAAL,iBAAA,GAAAD,WAAW,CAACM,IAAI,cAAAL,iBAAA,uBAAhBA,iBAAA,CAAkBM,IAAI,CAAC,IAAI,CAAC,KAAI;IACxC,CAAC,CAAC;IACFxB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyB,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC9C,KAAK,CAAC+C,OAAO,CAAC;MACZC,KAAK,EAAE,oBAAoB;MAC3BC,OAAO,EAAE,iFAAiF;MAC1FC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,QAAQ;MACpBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAM3B,QAAQ,GAAG,MAAMC,KAAK,CAAC,sBAAsBmB,EAAE,EAAE,EAAE;YACvDQ,MAAM,EAAE,QAAQ;YAChB1B,WAAW,EAAE;UACf,CAAC,CAAC;UACF,IAAIF,QAAQ,CAACG,EAAE,EAAE;YACftB,OAAO,CAACgD,OAAO,CAAC,kCAAkC,CAAC;YACnD9B,kBAAkB,CAAC,CAAC;UACtB,CAAC,MAAM;YACLlB,OAAO,CAACyB,KAAK,CAAC,8BAA8B,CAAC;UAC/C;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnDzB,OAAO,CAACyB,KAAK,CAAC,4BAA4B,CAAC;QAC7C;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwB,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,MAAMC,eAAe,GAAG;QACtB,GAAGD,MAAM;QACTjB,QAAQ,EAAEiB,MAAM,CAACjB,QAAQ,GAAGiB,MAAM,CAACjB,QAAQ,CAACmB,WAAW,CAAC,CAAC,GAAG,IAAI;QAChElB,SAAS,EAAEgB,MAAM,CAAChB,SAAS,GAAGgB,MAAM,CAAChB,SAAS,CAACkB,WAAW,CAAC,CAAC,GAAG,IAAI;QACnEjB,OAAO,EAAEe,MAAM,CAACf,OAAO,GAAGe,MAAM,CAACf,OAAO,CAACiB,WAAW,CAAC,CAAC,GAAG,IAAI;QAC7DhB,IAAI,EAAEc,MAAM,CAACd,IAAI,GAAGc,MAAM,CAACd,IAAI,CAACiB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,GAAW,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,GAAG;MAChG,CAAC;MAED,MAAMC,GAAG,GAAG7C,kBAAkB,GAAG,sBAAsBA,kBAAkB,CAACyB,EAAE,EAAE,GAAG,oBAAoB;MACrG,MAAMQ,MAAM,GAAGjC,kBAAkB,GAAG,KAAK,GAAG,MAAM;MAElD,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACuC,GAAG,EAAE;QAChCZ,MAAM;QACNa,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDvC,WAAW,EAAE,SAAS;QACtBwC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACZ,eAAe;MACtC,CAAC,CAAC;MAEF,IAAIhC,QAAQ,CAACG,EAAE,EAAE;QACftB,OAAO,CAACgD,OAAO,CAAC,eAAelC,kBAAkB,GAAG,SAAS,GAAG,SAAS,eAAe,CAAC;QACzFD,eAAe,CAAC,KAAK,CAAC;QACtBK,kBAAkB,CAAC,CAAC;MACtB,CAAC,MAAM;QACL,MAAM8C,SAAS,GAAG,MAAM7C,QAAQ,CAACK,IAAI,CAAC,CAAC;QACvCxB,OAAO,CAACyB,KAAK,CAACuC,SAAS,CAAChE,OAAO,IAAI,aAAac,kBAAkB,GAAG,QAAQ,GAAG,QAAQ,cAAc,CAAC;MACzG;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDzB,OAAO,CAACyB,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMwC,YAAY,GAAIC,IAAY,IAAK;IACrC,MAAMC,MAAM,GAAG;MACbC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE;IACf,CAAC;IACD,OAAOL,MAAM,CAACD,IAAI,CAAwB,IAAI,SAAS;EACzD,CAAC;EAED,MAAMO,YAAY,GAAIP,IAAY,IAAK;IACrC,MAAMQ,MAAM,GAAG;MACbN,UAAU,EAAE,YAAY;MACxBC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,YAAY;MACxBC,QAAQ,EAAE,UAAU;MACpBC,WAAW,EAAE;IACf,CAAC;IACD,OAAOE,MAAM,CAACR,IAAI,CAAwB,IAAIA,IAAI;EACpD,CAAC;EAED,MAAMS,SAAS,GAAI1C,QAAgB,IAAK;IACtC,OAAO,IAAI2C,IAAI,CAAC3C,QAAQ,CAAC,GAAG,IAAI2C,IAAI,CAAC,CAAC;EACxC,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEpC,KAAK,EAAE,OAAO;IACdqC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACC,IAAY,EAAEC,MAAmB,kBACxChF,OAAA;MAAAiF,QAAA,gBACEjF,OAAA;QAAKkF,SAAS,EAAC,aAAa;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzCtF,OAAA;QAAKkF,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAED,MAAM,CAACO;MAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D;EAET,CAAC,EACD;IACEhD,KAAK,EAAE,MAAM;IACbqC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGf,IAAY,iBACnB/D,OAAA,CAACJ,GAAG;MAAC4F,KAAK,EAAE1B,YAAY,CAACC,IAAI,CAAE;MAAAkB,QAAA,EAC5BX,YAAY,CAACP,IAAI;IAAC;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAET,CAAC,EACD;IACEhD,KAAK,EAAE,UAAU;IACjBqC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACW,QAAgB,EAAET,MAAmB,kBAC5ChF,OAAA;MAAAiF,QAAA,gBACEjF,OAAA;QAAAiF,QAAA,EAAMQ;MAAQ;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACpBN,MAAM,CAACU,QAAQ,iBAAI1F,OAAA,CAACJ,GAAG;QAACsF,SAAS,EAAC,SAAS;QAACM,KAAK,EAAC,OAAO;QAAAP,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE;EAET,CAAC,EACD;IACEhD,KAAK,EAAE,UAAU;IACjBqC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGhD,QAAgB,IAAK;MAC5B,MAAM6D,OAAO,GAAGnB,SAAS,CAAC1C,QAAQ,CAAC;MACnC,oBACE9B,OAAA;QAAKkF,SAAS,EAAES,OAAO,GAAG,cAAc,GAAG,EAAG;QAAAV,QAAA,GAC3C,IAAIR,IAAI,CAAC3C,QAAQ,CAAC,CAAC8D,kBAAkB,CAAC,CAAC,EACvCD,OAAO,iBAAI3F,OAAA;UAAKkF,SAAS,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAEV;EACF,CAAC,EACD;IACEhD,KAAK,EAAE,QAAQ;IACfqC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACe,QAAiB,EAAEb,MAAmB,KAAK;MAClD,MAAMW,OAAO,GAAGnB,SAAS,CAACQ,MAAM,CAAClD,QAAQ,CAAC;MAC1C,oBACE9B,OAAA,CAACJ,GAAG;QAAC4F,KAAK,EAAEK,QAAQ,IAAI,CAACF,OAAO,GAAG,OAAO,GAAG,KAAM;QAAAV,QAAA,EAChDU,OAAO,GAAG,SAAS,GAAGE,QAAQ,GAAG,QAAQ,GAAG;MAAU;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAEV;EACF,CAAC,EACD;IACEhD,KAAK,EAAE,MAAM;IACbqC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAG7C,IAAc,iBACrBjC,OAAA;MAAAiF,QAAA,GACGhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3C,GAAG,CAAC,CAACC,GAAG,EAAE2C,KAAK,kBAChC/F,OAAA,CAACJ,GAAG;QAAasF,SAAS,EAAC,SAAS;QAAAD,QAAA,EAAE7B;MAAG,GAA/B2C,KAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgC,CAChD,CAAC,EACD,CAAArD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,MAAM,IAAG,CAAC,iBAAIhG,OAAA,CAACJ,GAAG;QAACsF,SAAS,EAAC,SAAS;QAAAD,QAAA,GAAC,GAAC,EAAChD,IAAI,CAAC+D,MAAM,GAAG,CAAC;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE;EAET,CAAC,EACD;IACEhD,KAAK,EAAE,SAAS;IAChBqC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGmB,IAAY,IAAK,IAAIxB,IAAI,CAACwB,IAAI,CAAC,CAACL,kBAAkB,CAAC;EAC9D,CAAC,EACD;IACEtD,KAAK,EAAE,SAAS;IAChBsC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAEA,CAACoB,CAAM,EAAElB,MAAmB,kBAClChF,OAAA,CAACX,KAAK;MAAC8G,IAAI,EAAC,OAAO;MAAAlB,QAAA,gBACjBjF,OAAA,CAACb,MAAM;QACL4E,IAAI,EAAC,MAAM;QACXqC,IAAI,eAAEpG,OAAA,CAACd,WAAW;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBe,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAAC,kBAAkBvB,MAAM,CAAC5C,EAAE,EAAE,EAAE,QAAQ,CAAE;QACpEE,KAAK,EAAC;MAAM;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACFtF,OAAA,CAACb,MAAM;QACL4E,IAAI,EAAC,MAAM;QACXqC,IAAI,eAAEpG,OAAA,CAAChB,YAAY;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBe,OAAO,EAAEA,CAAA,KAAM3E,UAAU,CAACsD,MAAM,CAAE;QAClC1C,KAAK,EAAC;MAAM;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACFtF,OAAA,CAACb,MAAM;QACL4E,IAAI,EAAC,MAAM;QACXyC,MAAM;QACNJ,IAAI,eAAEpG,OAAA,CAACf,cAAc;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBe,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAAC6C,MAAM,CAAC5C,EAAE,CAAE;QACvCE,KAAK,EAAC;MAAQ;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAEX,CAAC,CACF;EAED,oBACEtF,OAAA;IAAKkF,SAAS,EAAC,KAAK;IAAAD,QAAA,gBAClBjF,OAAA;MAAKkF,SAAS,EAAC,wCAAwC;MAAAD,QAAA,gBACrDjF,OAAA;QAAAiF,QAAA,gBACEjF,OAAA;UAAIkF,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EtF,OAAA;UAAGkF,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAA2D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzF,CAAC,eACNtF,OAAA,CAACb,MAAM;QACL4E,IAAI,EAAC,SAAS;QACdqC,IAAI,eAAEpG,OAAA,CAACjB,YAAY;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBe,OAAO,EAAE7E,YAAa;QACtB2E,IAAI,EAAC,OAAO;QAAAlB,QAAA,EACb;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENtF,OAAA,CAACZ,KAAK;MACJsF,OAAO,EAAEA,OAAQ;MACjB+B,UAAU,EAAEpG,aAAc;MAC1BqG,MAAM,EAAC,IAAI;MACXnG,OAAO,EAAEA,OAAQ;MACjBoG,UAAU,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,IAAI;QACrBC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAGC,KAAK,IAAK,SAASA,KAAK;MACtC,CAAE;MACFC,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK;IAAE;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEFtF,OAAA,CAACV,KAAK;MACJgD,KAAK,EAAE3B,kBAAkB,GAAG,kBAAkB,GAAG,oBAAqB;MACtE4F,IAAI,EAAE9F,YAAa;MACnB0G,QAAQ,EAAEA,CAAA,KAAMzG,eAAe,CAAC,KAAK,CAAE;MACvC0G,MAAM,EAAE,IAAK;MACbvC,KAAK,EAAE,GAAI;MACXwC,cAAc;MAAApC,QAAA,eAEdjF,OAAA,CAACT,IAAI;QACHsB,IAAI,EAAEA,IAAK;QACXyG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEzE,YAAa;QACvB0E,aAAa,EAAE;UACb3B,QAAQ,EAAE,IAAI;UACdH,QAAQ,EAAE,KAAK;UACf3B,IAAI,EAAE;QACR,CAAE;QAAAkB,QAAA,gBAEFjF,OAAA;UAAKkF,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCjF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhI,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAAoF,QAAA,eAE/DjF,OAAA,CAACR,KAAK;cAACsI,WAAW,EAAC;YAAyB;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,MAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhI,OAAO,EAAE;YAAuB,CAAC,CAAE;YAAAoF,QAAA,eAE7DjF,OAAA,CAACP,MAAM;cAACqI,WAAW,EAAC,aAAa;cAAA7C,QAAA,gBAC/BjF,OAAA,CAACE,MAAM;gBAAC6H,KAAK,EAAC,YAAY;gBAAA9C,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CtF,OAAA,CAACE,MAAM;gBAAC6H,KAAK,EAAC,UAAU;gBAAA9C,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CtF,OAAA,CAACE,MAAM;gBAAC6H,KAAK,EAAC,YAAY;gBAAA9C,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CtF,OAAA,CAACE,MAAM;gBAAC6H,KAAK,EAAC,UAAU;gBAAA9C,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CtF,OAAA,CAACE,MAAM;gBAAC6H,KAAK,EAAC,aAAa;gBAAA9C,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtF,OAAA,CAACT,IAAI,CAACkI,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEhI,OAAO,EAAE;UAA+B,CAAC,CAAE;UAAAoF,QAAA,eAErEjF,OAAA,CAACC,QAAQ;YAAC+H,IAAI,EAAE,CAAE;YAACF,WAAW,EAAC;UAAyC;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eAEZtF,OAAA;UAAKkF,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCjF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhI,OAAO,EAAE;YAAgC,CAAC,CAAE;YAAAoF,QAAA,eAEtEjF,OAAA,CAACR,KAAK;cAACsI,WAAW,EAAC;YAAmB;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhI,OAAO,EAAE;YAA4B,CAAC,CAAE;YAAAoF,QAAA,eAElEjF,OAAA,CAACR,KAAK;cAACsI,WAAW,EAAC;YAAe;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtF,OAAA;UAAKkF,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCjF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAC,sBAAsB;YAC5BC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhI,OAAO,EAAE;YAA6B,CAAC,CAAE;YAAAoF,QAAA,eAEnEjF,OAAA,CAACL,UAAU;cAACuF,SAAS,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAC,YAAY;YAAA1C,QAAA,eAElBjF,OAAA,CAACL,UAAU;cAACuF,SAAS,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,SAAS;YACdC,KAAK,EAAC,UAAU;YAAA1C,QAAA,eAEhBjF,OAAA,CAACL,UAAU;cAACuF,SAAS,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtF,OAAA;UAAKkF,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCjF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAC,kBAAkB;YAAA1C,QAAA,eAExBjF,OAAA,CAACR,KAAK;cAACsI,WAAW,EAAC;YAA2B;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,SAAS;YACdC,KAAK,EAAC,SAAS;YAAA1C,QAAA,eAEfjF,OAAA,CAACR,KAAK;cAACsI,WAAW,EAAC;YAAqB;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtF,OAAA;UAAKkF,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCjF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAC,eAAe;YAAA1C,QAAA,eAErBjF,OAAA,CAACR,KAAK;cAACsI,WAAW,EAAC;YAAqB;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAC,eAAe;YAAA1C,QAAA,eAErBjF,OAAA,CAACR,KAAK;cAACsI,WAAW,EAAC;YAA+B;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtF,OAAA,CAACT,IAAI,CAACkI,IAAI;UACRC,IAAI,EAAC,cAAc;UACnBC,KAAK,EAAC,cAAc;UAAA1C,QAAA,eAEpBjF,OAAA,CAACC,QAAQ;YAAC+H,IAAI,EAAE,CAAE;YAACF,WAAW,EAAC;UAAiC;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,UAAU;UAAA1C,QAAA,eAEhBjF,OAAA,CAACC,QAAQ;YAAC+H,IAAI,EAAE,CAAE;YAACF,WAAW,EAAC;UAA2B;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,MAAM;UACZM,IAAI,EAAC,sBAAsB;UAAAhD,QAAA,eAE3BjF,OAAA,CAACR,KAAK;YAACsI,WAAW,EAAC;UAA0B;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eAEZtF,OAAA;UAAKkF,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCjF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAC,aAAa;YACnBO,aAAa,EAAC,SAAS;YAAAjD,QAAA,eAEvBjF,OAAA,CAACN,MAAM;cAACyI,eAAe,EAAC,QAAQ;cAACC,iBAAiB,EAAC;YAAS;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEZtF,OAAA,CAACT,IAAI,CAACkI,IAAI;YACRC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAC,QAAQ;YACdO,aAAa,EAAC,SAAS;YAAAjD,QAAA,eAEvBjF,OAAA,CAACN,MAAM;cAACyI,eAAe,EAAC,QAAQ;cAACC,iBAAiB,EAAC;YAAU;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtF,OAAA;UAAKkF,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvDjF,OAAA,CAACb,MAAM;YAACkH,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,KAAK,CAAE;YAAAuE,QAAA,EAAC;UAE/C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtF,OAAA,CAACb,MAAM;YAAC4E,IAAI,EAAC,SAAS;YAACsE,QAAQ,EAAC,QAAQ;YAAApD,QAAA,GACrCtE,kBAAkB,GAAG,QAAQ,GAAG,QAAQ,EAAC,cAC5C;UAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAClF,EAAA,CA7cID,kBAA4B;EAAA,QAKjBZ,IAAI,CAACuB,OAAO;AAAA;AAAAwH,EAAA,GALvBnI,kBAA4B;AA+clC,eAAeA,kBAAkB;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}