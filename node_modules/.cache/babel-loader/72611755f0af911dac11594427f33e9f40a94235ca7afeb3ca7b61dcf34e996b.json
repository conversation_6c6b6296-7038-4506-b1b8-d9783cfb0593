{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Opportunities = () => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [opportunities, setOpportunities] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    type: '',\n    location: '',\n    isRemote: ''\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  useEffect(() => {\n    fetchOpportunities();\n  }, [filters]);\n  const fetchOpportunities = async () => {\n    try {\n      const params = new URLSearchParams({\n        active: 'true',\n        orderBy: 'deadline',\n        orderDirection: 'ASC',\n        ...filters\n      });\n      const response = await fetch(`/api/opportunities?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setOpportunities(data.data.opportunities || []);\n      } else {\n        console.error('Failed to fetch opportunities');\n      }\n    } catch (error) {\n      console.error('Error fetching opportunities:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = async () => {\n    if (searchTerm.trim() === '') {\n      fetchOpportunities();\n      return;\n    }\n    try {\n      const params = new URLSearchParams({\n        q: searchTerm,\n        ...filters\n      });\n      const response = await fetch(`/api/opportunities/search?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setOpportunities(data.data.opportunities || []);\n      }\n    } catch (error) {\n      console.error('Error searching opportunities:', error);\n    }\n  };\n  const getTypeIcon = type => {\n    const icons = {\n      internship: '🎓',\n      training: '📚',\n      conference: '🎤',\n      workshop: '🔧',\n      competition: '🏆'\n    };\n    return icons[type] || '📋';\n  };\n  const getTypeColor = type => {\n    const colors = {\n      internship: 'bg-blue-100 text-blue-800',\n      training: 'bg-green-100 text-green-800',\n      conference: 'bg-purple-100 text-purple-800',\n      workshop: 'bg-orange-100 text-orange-800',\n      competition: 'bg-red-100 text-red-800'\n    };\n    return colors[type] || 'bg-gray-100 text-gray-800';\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const isExpired = deadline => {\n    return new Date(deadline) < new Date();\n  };\n  const getDaysUntilDeadline = deadline => {\n    const today = new Date();\n    const deadlineDate = new Date(deadline);\n    const diffTime = deadlineDate.getTime() - today.getTime();\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600\",\n            children: \"Chargement des opportunit\\xE9s...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-bold mb-4\",\n            children: translations.opportunities.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-blue-100 max-w-3xl mx-auto\",\n            children: translations.opportunities.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Rechercher des opportunit\\xE9s...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && handleSearch(),\n                className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.type,\n              onChange: e => setFilters({\n                ...filters,\n                type: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: translations.opportunities.filters.all\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"internship\",\n                children: translations.opportunities.types.internship\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"training\",\n                children: translations.opportunities.types.training\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"conference\",\n                children: translations.opportunities.types.conference\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"workshop\",\n                children: translations.opportunities.types.workshop\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"competition\",\n                children: translations.opportunities.types.competition\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filters.isRemote,\n              onChange: e => setFilters({\n                ...filters,\n                isRemote: e.target.value\n              }),\n              className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Pr\\xE9sentiel et distanciel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"Distanciel uniquement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Pr\\xE9sentiel uniquement\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\",\n            children: \"Rechercher\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n        children: Object.entries(translations.opportunities.types).map(([key, label]) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setFilters({\n            ...filters,\n            type: filters.type === key ? '' : key\n          }),\n          className: `p-4 rounded-lg border-2 transition-all duration-200 ${filters.type === key ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl mb-2\",\n            children: getTypeIcon(key)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-sm\",\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: opportunities.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Aucune opportunit\\xE9 trouv\\xE9e\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Essayez de modifier vos filtres ou votre recherche.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: opportunities.map(opportunity => {\n          const expired = isExpired(opportunity.deadline);\n          const daysLeft = getDaysUntilDeadline(opportunity.deadline);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 ${expired ? 'opacity-75' : 'hover:border-blue-200'}`,\n            children: [opportunity.thumbnail && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"aspect-w-16 aspect-h-9 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: opportunity.thumbnail,\n                alt: opportunity.title,\n                className: \"w-full h-48 object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(opportunity.type)}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mr-1\",\n                    children: getTypeIcon(opportunity.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 25\n                  }, this), translations.opportunities.types[opportunity.type]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 23\n                }, this), opportunity.isRemote && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                  children: \"\\uD83C\\uDF10 Distanciel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\",\n                children: opportunity.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm mb-3 line-clamp-3\",\n                children: opportunity.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-4 w-4 mr-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 25\n                  }, this), opportunity.organization]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-4 w-4 mr-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this), opportunity.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex items-center text-sm ${expired ? 'text-red-600' : daysLeft <= 7 ? 'text-orange-600' : 'text-gray-600'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"h-4 w-4 mr-2\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this), expired ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: translations.opportunities.expired\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Date limite: \", formatDate(opportunity.deadline), daysLeft <= 7 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-1 font-medium\",\n                      children: [\"(\", daysLeft, \" jour\", daysLeft !== 1 ? 's' : '', \" restant\", daysLeft !== 1 ? 's' : '', \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this), opportunity.tags && opportunity.tags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4 flex flex-wrap gap-1\",\n                children: opportunity.tags.slice(0, 3).map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\",\n                  children: [\"#\", tag]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: `/opportunities/${opportunity.id}`,\n                  className: \"text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors duration-200\",\n                  children: translations.opportunities.viewDetails\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this), opportunity.applicationLink && !expired && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: opportunity.applicationLink,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200\",\n                  children: translations.opportunities.apply\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this)]\n          }, opportunity.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(Opportunities, \"Sm0hU70rZtD7TNxCku0HhfctoMY=\", false, function () {\n  return [useLanguage];\n});\n_c = Opportunities;\nexport default Opportunities;\nvar _c;\n$RefreshReg$(_c, \"Opportunities\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLanguage", "jsxDEV", "_jsxDEV", "Opportunities", "_s", "translations", "opportunities", "setOpportunities", "loading", "setLoading", "filters", "setFilters", "type", "location", "isRemote", "searchTerm", "setSearchTerm", "fetchOpportunities", "params", "URLSearchParams", "active", "orderBy", "orderDirection", "response", "fetch", "ok", "data", "json", "console", "error", "handleSearch", "trim", "q", "getTypeIcon", "icons", "internship", "training", "conference", "workshop", "competition", "getTypeColor", "colors", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "isExpired", "deadline", "getDaysUntilDeadline", "today", "deadlineDate", "diffTime", "getTime", "Math", "ceil", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "subtitle", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "key", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "all", "types", "onClick", "Object", "entries", "map", "label", "length", "opportunity", "expired", "daysLeft", "thumbnail", "src", "alt", "description", "organization", "tags", "slice", "tag", "index", "to", "id", "viewDetails", "applicationLink", "href", "rel", "apply", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\n\ninterface Opportunity {\n  id: number;\n  title: string;\n  description: string;\n  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';\n  organization: string;\n  location: string;\n  isRemote: boolean;\n  deadline: string;\n  startDate?: string;\n  endDate?: string;\n  applicationLink?: string;\n  thumbnail?: string;\n  isActive: boolean;\n  tags?: string[];\n}\n\nconst Opportunities: React.FC = () => {\n  const { translations } = useLanguage();\n  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState({\n    type: '',\n    location: '',\n    isRemote: ''\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    fetchOpportunities();\n  }, [filters]);\n\n  const fetchOpportunities = async () => {\n    try {\n      const params = new URLSearchParams({\n        active: 'true',\n        orderBy: 'deadline',\n        orderDirection: 'ASC',\n        ...filters\n      });\n\n      const response = await fetch(`/api/opportunities?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setOpportunities(data.data.opportunities || []);\n      } else {\n        console.error('Failed to fetch opportunities');\n      }\n    } catch (error) {\n      console.error('Error fetching opportunities:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = async () => {\n    if (searchTerm.trim() === '') {\n      fetchOpportunities();\n      return;\n    }\n\n    try {\n      const params = new URLSearchParams({\n        q: searchTerm,\n        ...filters\n      });\n\n      const response = await fetch(`/api/opportunities/search?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setOpportunities(data.data.opportunities || []);\n      }\n    } catch (error) {\n      console.error('Error searching opportunities:', error);\n    }\n  };\n\n  const getTypeIcon = (type: string): string => {\n    const icons = {\n      internship: '🎓',\n      training: '📚',\n      conference: '🎤',\n      workshop: '🔧',\n      competition: '🏆'\n    };\n    return icons[type as keyof typeof icons] || '📋';\n  };\n\n  const getTypeColor = (type: string): string => {\n    const colors = {\n      internship: 'bg-blue-100 text-blue-800',\n      training: 'bg-green-100 text-green-800',\n      conference: 'bg-purple-100 text-purple-800',\n      workshop: 'bg-orange-100 text-orange-800',\n      competition: 'bg-red-100 text-red-800'\n    };\n    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';\n  };\n\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const isExpired = (deadline: string): boolean => {\n    return new Date(deadline) < new Date();\n  };\n\n  const getDaysUntilDeadline = (deadline: string): number => {\n    const today = new Date();\n    const deadlineDate = new Date(deadline);\n    const diffTime = deadlineDate.getTime() - today.getTime();\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Chargement des opportunités...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n              {translations.opportunities.title}\n            </h1>\n            <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n              {translations.opportunities.subtitle}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4\">\n            <div className=\"md:col-span-2\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Rechercher des opportunités...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                  className=\"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n            \n            <div>\n              <select\n                value={filters.type}\n                onChange={(e) => setFilters({ ...filters, type: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">{translations.opportunities.filters.all}</option>\n                <option value=\"internship\">{translations.opportunities.types.internship}</option>\n                <option value=\"training\">{translations.opportunities.types.training}</option>\n                <option value=\"conference\">{translations.opportunities.types.conference}</option>\n                <option value=\"workshop\">{translations.opportunities.types.workshop}</option>\n                <option value=\"competition\">{translations.opportunities.types.competition}</option>\n              </select>\n            </div>\n            \n            <div>\n              <select\n                value={filters.isRemote}\n                onChange={(e) => setFilters({ ...filters, isRemote: e.target.value })}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">Présentiel et distanciel</option>\n                <option value=\"true\">Distanciel uniquement</option>\n                <option value=\"false\">Présentiel uniquement</option>\n              </select>\n            </div>\n          </div>\n          \n          <div className=\"flex justify-center\">\n            <button\n              onClick={handleSearch}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n            >\n              Rechercher\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Type Quick Filters */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\">\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\n          {Object.entries(translations.opportunities.types).map(([key, label]) => (\n            <button\n              key={key}\n              onClick={() => setFilters({ ...filters, type: filters.type === key ? '' : key })}\n              className={`p-4 rounded-lg border-2 transition-all duration-200 ${\n                filters.type === key\n                  ? 'border-blue-500 bg-blue-50 text-blue-700'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"text-2xl mb-2\">{getTypeIcon(key)}</div>\n              <div className=\"font-medium text-sm\">{label}</div>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Opportunities Grid */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        {opportunities.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">🔍</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              Aucune opportunité trouvée\n            </h3>\n            <p className=\"text-gray-600\">\n              Essayez de modifier vos filtres ou votre recherche.\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {opportunities.map((opportunity) => {\n              const expired = isExpired(opportunity.deadline);\n              const daysLeft = getDaysUntilDeadline(opportunity.deadline);\n              \n              return (\n                <div\n                  key={opportunity.id}\n                  className={`bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 ${\n                    expired ? 'opacity-75' : 'hover:border-blue-200'\n                  }`}\n                >\n                  {opportunity.thumbnail && (\n                    <div className=\"aspect-w-16 aspect-h-9 overflow-hidden\">\n                      <img\n                        src={opportunity.thumbnail}\n                        alt={opportunity.title}\n                        className=\"w-full h-48 object-cover\"\n                      />\n                    </div>\n                  )}\n                  \n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(opportunity.type)}`}>\n                        <span className=\"mr-1\">{getTypeIcon(opportunity.type)}</span>\n                        {translations.opportunities.types[opportunity.type]}\n                      </span>\n                      \n                      {opportunity.isRemote && (\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                          🌐 Distanciel\n                        </span>\n                      )}\n                    </div>\n                    \n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\">\n                      {opportunity.title}\n                    </h3>\n                    \n                    <p className=\"text-gray-600 text-sm mb-3 line-clamp-3\">\n                      {opportunity.description}\n                    </p>\n                    \n                    <div className=\"space-y-2 mb-4\">\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                        </svg>\n                        {opportunity.organization}\n                      </div>\n                      \n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                        {opportunity.location}\n                      </div>\n                      \n                      <div className={`flex items-center text-sm ${expired ? 'text-red-600' : daysLeft <= 7 ? 'text-orange-600' : 'text-gray-600'}`}>\n                        <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                        </svg>\n                        {expired ? (\n                          <span className=\"font-medium\">{translations.opportunities.expired}</span>\n                        ) : (\n                          <span>\n                            Date limite: {formatDate(opportunity.deadline)}\n                            {daysLeft <= 7 && (\n                              <span className=\"ml-1 font-medium\">\n                                ({daysLeft} jour{daysLeft !== 1 ? 's' : ''} restant{daysLeft !== 1 ? 's' : ''})\n                              </span>\n                            )}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                    \n                    {opportunity.tags && opportunity.tags.length > 0 && (\n                      <div className=\"mb-4 flex flex-wrap gap-1\">\n                        {opportunity.tags.slice(0, 3).map((tag, index) => (\n                          <span\n                            key={index}\n                            className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\"\n                          >\n                            #{tag}\n                          </span>\n                        ))}\n                      </div>\n                    )}\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <Link\n                        to={`/opportunities/${opportunity.id}`}\n                        className=\"text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors duration-200\"\n                      >\n                        {translations.opportunities.viewDetails}\n                      </Link>\n                      \n                      {opportunity.applicationLink && !expired && (\n                        <a\n                          href={opportunity.applicationLink}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n                        >\n                          {translations.opportunities.apply}\n                        </a>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Opportunities;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmBzD,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAa,CAAC,GAAGL,WAAW,CAAC,CAAC;EACtC,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAgB,EAAE,CAAC;EACrE,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC;IACrCe,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdmB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACP,OAAO,CAAC,CAAC;EAEb,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAU;QACnBC,cAAc,EAAE,KAAK;QACrB,GAAGZ;MACL,CAAC,CAAC;MAEF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,sBAAsBN,MAAM,EAAE,CAAC;MAC5D,IAAIK,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCpB,gBAAgB,CAACmB,IAAI,CAACA,IAAI,CAACpB,aAAa,IAAI,EAAE,CAAC;MACjD,CAAC,MAAM;QACLsB,OAAO,CAACC,KAAK,CAAC,+BAA+B,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIf,UAAU,CAACgB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5Bd,kBAAkB,CAAC,CAAC;MACpB;IACF;IAEA,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCa,CAAC,EAAEjB,UAAU;QACb,GAAGL;MACL,CAAC,CAAC;MAEF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,6BAA6BN,MAAM,EAAE,CAAC;MACnE,IAAIK,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCpB,gBAAgB,CAACmB,IAAI,CAACA,IAAI,CAACpB,aAAa,IAAI,EAAE,CAAC;MACjD;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMI,WAAW,GAAIrB,IAAY,IAAa;IAC5C,MAAMsB,KAAK,GAAG;MACZC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC;IACD,OAAOL,KAAK,CAACtB,IAAI,CAAuB,IAAI,IAAI;EAClD,CAAC;EAED,MAAM4B,YAAY,GAAI5B,IAAY,IAAa;IAC7C,MAAM6B,MAAM,GAAG;MACbN,UAAU,EAAE,2BAA2B;MACvCC,QAAQ,EAAE,6BAA6B;MACvCC,UAAU,EAAE,+BAA+B;MAC3CC,QAAQ,EAAE,+BAA+B;MACzCC,WAAW,EAAE;IACf,CAAC;IACD,OAAOE,MAAM,CAAC7B,IAAI,CAAwB,IAAI,2BAA2B;EAC3E,CAAC;EAED,MAAM8B,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,SAAS,GAAIC,QAAgB,IAAc;IAC/C,OAAO,IAAIN,IAAI,CAACM,QAAQ,CAAC,GAAG,IAAIN,IAAI,CAAC,CAAC;EACxC,CAAC;EAED,MAAMO,oBAAoB,GAAID,QAAgB,IAAa;IACzD,MAAME,KAAK,GAAG,IAAIR,IAAI,CAAC,CAAC;IACxB,MAAMS,YAAY,GAAG,IAAIT,IAAI,CAACM,QAAQ,CAAC;IACvC,MAAMI,QAAQ,GAAGD,YAAY,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACzD,OAAOC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EACpD,CAAC;EAED,IAAI/C,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKyD,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9E1D,OAAA;QAAKyD,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D1D,OAAA;UAAKyD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1D,OAAA;YAAKyD,SAAS,EAAC;UAAwE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9F9D,OAAA;YAAGyD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9D,OAAA;IAAKyD,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExE1D,OAAA;MAAKyD,SAAS,EAAC,+DAA+D;MAAAC,QAAA,eAC5E1D,OAAA;QAAKyD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD1D,OAAA;UAAKyD,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B1D,OAAA;YAAIyD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAChDvD,YAAY,CAACC,aAAa,CAAC2D;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACL9D,OAAA;YAAGyD,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACnDvD,YAAY,CAACC,aAAa,CAAC4D;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D1D,OAAA;QAAKyD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD1D,OAAA;UAAKyD,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD1D,OAAA;YAAKyD,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B1D,OAAA;cAAKyD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1D,OAAA;gBACEU,IAAI,EAAC,MAAM;gBACXuD,WAAW,EAAC,mCAAgC;gBAC5CC,KAAK,EAAErD,UAAW;gBAClBsD,QAAQ,EAAGC,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CI,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAI3C,YAAY,CAAC,CAAE;gBACvD6B,SAAS,EAAC;cAAoH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H,CAAC,eACF9D,OAAA;gBAAKyD,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF1D,OAAA;kBAAKyD,SAAS,EAAC,uBAAuB;kBAACe,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eAC1F1D,OAAA;oBAAM2E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA6C;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9D,OAAA;YAAA0D,QAAA,eACE1D,OAAA;cACEkE,KAAK,EAAE1D,OAAO,CAACE,IAAK;cACpByD,QAAQ,EAAGC,CAAC,IAAK3D,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEE,IAAI,EAAE0D,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAClET,SAAS,EAAC,8GAA8G;cAAAC,QAAA,gBAExH1D,OAAA;gBAAQkE,KAAK,EAAC,EAAE;gBAAAR,QAAA,EAAEvD,YAAY,CAACC,aAAa,CAACI,OAAO,CAACuE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAClE9D,OAAA;gBAAQkE,KAAK,EAAC,YAAY;gBAAAR,QAAA,EAAEvD,YAAY,CAACC,aAAa,CAAC4E,KAAK,CAAC/C;cAAU;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACjF9D,OAAA;gBAAQkE,KAAK,EAAC,UAAU;gBAAAR,QAAA,EAAEvD,YAAY,CAACC,aAAa,CAAC4E,KAAK,CAAC9C;cAAQ;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC7E9D,OAAA;gBAAQkE,KAAK,EAAC,YAAY;gBAAAR,QAAA,EAAEvD,YAAY,CAACC,aAAa,CAAC4E,KAAK,CAAC7C;cAAU;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACjF9D,OAAA;gBAAQkE,KAAK,EAAC,UAAU;gBAAAR,QAAA,EAAEvD,YAAY,CAACC,aAAa,CAAC4E,KAAK,CAAC5C;cAAQ;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC7E9D,OAAA;gBAAQkE,KAAK,EAAC,aAAa;gBAAAR,QAAA,EAAEvD,YAAY,CAACC,aAAa,CAAC4E,KAAK,CAAC3C;cAAW;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN9D,OAAA;YAAA0D,QAAA,eACE1D,OAAA;cACEkE,KAAK,EAAE1D,OAAO,CAACI,QAAS;cACxBuD,QAAQ,EAAGC,CAAC,IAAK3D,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEI,QAAQ,EAAEwD,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACtET,SAAS,EAAC,8GAA8G;cAAAC,QAAA,gBAExH1D,OAAA;gBAAQkE,KAAK,EAAC,EAAE;gBAAAR,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClD9D,OAAA;gBAAQkE,KAAK,EAAC,MAAM;gBAAAR,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnD9D,OAAA;gBAAQkE,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC1D,OAAA;YACEiF,OAAO,EAAErD,YAAa;YACtB6B,SAAS,EAAC,8FAA8F;YAAAC,QAAA,EACzG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D1D,OAAA;QAAKyD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDwB,MAAM,CAACC,OAAO,CAAChF,YAAY,CAACC,aAAa,CAAC4E,KAAK,CAAC,CAACI,GAAG,CAAC,CAAC,CAACb,GAAG,EAAEc,KAAK,CAAC,kBACjErF,OAAA;UAEEiF,OAAO,EAAEA,CAAA,KAAMxE,UAAU,CAAC;YAAE,GAAGD,OAAO;YAAEE,IAAI,EAAEF,OAAO,CAACE,IAAI,KAAK6D,GAAG,GAAG,EAAE,GAAGA;UAAI,CAAC,CAAE;UACjFd,SAAS,EAAE,uDACTjD,OAAO,CAACE,IAAI,KAAK6D,GAAG,GAChB,0CAA0C,GAC1C,iEAAiE,EACpE;UAAAb,QAAA,gBAEH1D,OAAA;YAAKyD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAE3B,WAAW,CAACwC,GAAG;UAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvD9D,OAAA;YAAKyD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAE2B;UAAK;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAT7CS,GAAG;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUF,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAC1DtD,aAAa,CAACkF,MAAM,KAAK,CAAC,gBACzBtF,OAAA;QAAKyD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1D,OAAA;UAAKyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC9D,OAAA;UAAIyD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9D,OAAA;UAAGyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAEN9D,OAAA;QAAKyD,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEtD,aAAa,CAACgF,GAAG,CAAEG,WAAW,IAAK;UAClC,MAAMC,OAAO,GAAGxC,SAAS,CAACuC,WAAW,CAACtC,QAAQ,CAAC;UAC/C,MAAMwC,QAAQ,GAAGvC,oBAAoB,CAACqC,WAAW,CAACtC,QAAQ,CAAC;UAE3D,oBACEjD,OAAA;YAEEyD,SAAS,EAAE,oHACT+B,OAAO,GAAG,YAAY,GAAG,uBAAuB,EAC/C;YAAA9B,QAAA,GAEF6B,WAAW,CAACG,SAAS,iBACpB1F,OAAA;cAAKyD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,eACrD1D,OAAA;gBACE2F,GAAG,EAAEJ,WAAW,CAACG,SAAU;gBAC3BE,GAAG,EAAEL,WAAW,CAACxB,KAAM;gBACvBN,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAED9D,OAAA;cAAKyD,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB1D,OAAA;gBAAKyD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD1D,OAAA;kBAAMyD,SAAS,EAAE,2EAA2EnB,YAAY,CAACiD,WAAW,CAAC7E,IAAI,CAAC,EAAG;kBAAAgD,QAAA,gBAC3H1D,OAAA;oBAAMyD,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAE3B,WAAW,CAACwD,WAAW,CAAC7E,IAAI;kBAAC;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC5D3D,YAAY,CAACC,aAAa,CAAC4E,KAAK,CAACO,WAAW,CAAC7E,IAAI,CAAC;gBAAA;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,EAENyB,WAAW,CAAC3E,QAAQ,iBACnBZ,OAAA;kBAAMyD,SAAS,EAAC,qGAAqG;kBAAAC,QAAA,EAAC;gBAEtH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9D,OAAA;gBAAIyD,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAClE6B,WAAW,CAACxB;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eAEL9D,OAAA;gBAAGyD,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACnD6B,WAAW,CAACM;cAAW;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAEJ9D,OAAA;gBAAKyD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B1D,OAAA;kBAAKyD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtD1D,OAAA;oBAAKyD,SAAS,EAAC,cAAc;oBAACe,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAhB,QAAA,eACjF1D,OAAA;sBAAM2E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA2I;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChN,CAAC,EACLyB,WAAW,CAACO,YAAY;gBAAA;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eAEN9D,OAAA;kBAAKyD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtD1D,OAAA;oBAAKyD,SAAS,EAAC,cAAc;oBAACe,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAhB,QAAA,gBACjF1D,OAAA;sBAAM2E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAoF;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5J9D,OAAA;sBAAM2E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAkC;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvG,CAAC,EACLyB,WAAW,CAAC5E,QAAQ;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eAEN9D,OAAA;kBAAKyD,SAAS,EAAE,6BAA6B+B,OAAO,GAAG,cAAc,GAAGC,QAAQ,IAAI,CAAC,GAAG,iBAAiB,GAAG,eAAe,EAAG;kBAAA/B,QAAA,gBAC5H1D,OAAA;oBAAKyD,SAAS,EAAC,cAAc;oBAACe,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAhB,QAAA,eACjF1D,OAAA;sBAAM2E,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAwF;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7J,CAAC,EACL0B,OAAO,gBACNxF,OAAA;oBAAMyD,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAEvD,YAAY,CAACC,aAAa,CAACoF;kBAAO;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,gBAEzE9D,OAAA;oBAAA0D,QAAA,GAAM,eACS,EAAClB,UAAU,CAAC+C,WAAW,CAACtC,QAAQ,CAAC,EAC7CwC,QAAQ,IAAI,CAAC,iBACZzF,OAAA;sBAAMyD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,GAAC,GAChC,EAAC+B,QAAQ,EAAC,OAAK,EAACA,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,UAAQ,EAACA,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,GAChF;oBAAA;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELyB,WAAW,CAACQ,IAAI,IAAIR,WAAW,CAACQ,IAAI,CAACT,MAAM,GAAG,CAAC,iBAC9CtF,OAAA;gBAAKyD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvC6B,WAAW,CAACQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACZ,GAAG,CAAC,CAACa,GAAG,EAAEC,KAAK,kBAC3ClG,OAAA;kBAEEyD,SAAS,EAAC,kEAAkE;kBAAAC,QAAA,GAC7E,GACE,EAACuC,GAAG;gBAAA,GAHAC,KAAK;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIN,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eAED9D,OAAA;gBAAKyD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD1D,OAAA,CAACH,IAAI;kBACHsG,EAAE,EAAE,kBAAkBZ,WAAW,CAACa,EAAE,EAAG;kBACvC3C,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,EAE/FvD,YAAY,CAACC,aAAa,CAACiG;gBAAW;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EAENyB,WAAW,CAACe,eAAe,IAAI,CAACd,OAAO,iBACtCxF,OAAA;kBACEuG,IAAI,EAAEhB,WAAW,CAACe,eAAgB;kBAClCjC,MAAM,EAAC,QAAQ;kBACfmC,GAAG,EAAC,qBAAqB;kBACzB/C,SAAS,EAAC,kHAAkH;kBAAAC,QAAA,EAE3HvD,YAAY,CAACC,aAAa,CAACqG;gBAAK;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAxGDyB,WAAW,CAACa,EAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyGhB,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAzVID,aAAuB;EAAA,QACFH,WAAW;AAAA;AAAA4G,EAAA,GADhCzG,aAAuB;AA2V7B,eAAeA,aAAa;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}