{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Guides = () => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [guides, setGuides] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  useEffect(() => {\n    fetchGuides();\n  }, [selectedCategory]);\n  const fetchGuides = async () => {\n    try {\n      const params = new URLSearchParams({\n        published: 'true',\n        orderBy: 'created_at',\n        orderDirection: 'DESC'\n      });\n      if (selectedCategory) {\n        params.append('category', selectedCategory);\n      }\n      const response = await fetch(`/api/guides?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setGuides(data.data.guides || []);\n      } else {\n        console.error('Failed to fetch guides');\n      }\n    } catch (error) {\n      console.error('Error fetching guides:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = async () => {\n    if (searchTerm.trim() === '') {\n      fetchGuides();\n      return;\n    }\n    try {\n      const params = new URLSearchParams({\n        q: searchTerm,\n        category: selectedCategory\n      });\n      const response = await fetch(`/api/guides/search?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setGuides(data.data.guides || []);\n      }\n    } catch (error) {\n      console.error('Error searching guides:', error);\n    }\n  };\n  const getCategoryIcon = category => {\n    const icons = {\n      application: '📝',\n      documents: '📄',\n      preparation: '🎯',\n      tips: '💡'\n    };\n    return icons[category] || '📚';\n  };\n  const getCategoryColor = category => {\n    const colors = {\n      application: 'bg-blue-100 text-blue-800',\n      documents: 'bg-green-100 text-green-800',\n      preparation: 'bg-purple-100 text-purple-800',\n      tips: 'bg-yellow-100 text-yellow-800'\n    };\n    return colors[category] || 'bg-gray-100 text-gray-800';\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4 text-gray-600\",\n            children: \"Chargement des guides...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-5xl font-bold mb-4\",\n            children: translations.guides.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-blue-100 max-w-3xl mx-auto\",\n            children: translations.guides.subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Rechercher dans les guides...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && handleSearch(),\n                className: \"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Toutes les cat\\xE9gories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"application\",\n                children: translations.guides.categories.application\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"documents\",\n                children: translations.guides.categories.documents\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"preparation\",\n                children: translations.guides.categories.preparation\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"tips\",\n                children: translations.guides.categories.tips\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\",\n            children: \"Rechercher\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: Object.entries(translations.guides.categories).map(([key, label]) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSelectedCategory(selectedCategory === key ? '' : key),\n          className: `p-4 rounded-lg border-2 transition-all duration-200 ${selectedCategory === key ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl mb-2\",\n            children: getCategoryIcon(key)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium\",\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: guides.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDCDA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Aucun guide trouv\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Essayez de modifier votre recherche ou parcourez toutes les cat\\xE9gories.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: guides.map(guide => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/guides/${guide.slug}`,\n          className: \"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200\",\n          children: [guide.thumbnail && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"aspect-w-16 aspect-h-9 overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: guide.thumbnail,\n              alt: guide.title,\n              className: \"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(guide.category)}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-1\",\n                  children: getCategoryIcon(guide.category)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this), translations.guides.categories[guide.category]]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this), guide.readTime && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [guide.readTime, \" min\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-2 line-clamp-2\",\n              children: guide.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 19\n            }, this), guide.excerpt && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm mb-4 line-clamp-3\",\n              children: guide.excerpt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: formatDate(guide.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-blue-600 text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translations.guides.readMore\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 19\n            }, this), guide.tags && guide.tags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex flex-wrap gap-1\",\n              children: guide.tags.slice(0, 3).map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\",\n                children: [\"#\", tag]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 17\n          }, this)]\n        }, guide.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(Guides, \"Ue7xC4CNefGIda/BfmPd59hIBgk=\", false, function () {\n  return [useLanguage];\n});\n_c = Guides;\nexport default Guides;\nvar _c;\n$RefreshReg$(_c, \"Guides\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLanguage", "jsxDEV", "_jsxDEV", "Guides", "_s", "translations", "guides", "setGuides", "loading", "setLoading", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "searchTerm", "setSearchTerm", "fetchGuides", "params", "URLSearchParams", "published", "orderBy", "orderDirection", "append", "response", "fetch", "ok", "data", "json", "console", "error", "handleSearch", "trim", "q", "category", "getCategoryIcon", "icons", "application", "documents", "preparation", "tips", "getCategoryColor", "colors", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "subtitle", "type", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "key", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "categories", "onClick", "Object", "entries", "map", "label", "length", "guide", "to", "slug", "thumbnail", "src", "alt", "readTime", "excerpt", "createdAt", "readMore", "tags", "slice", "tag", "index", "id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\n\ninterface Guide {\n  id: number;\n  title: string;\n  excerpt?: string;\n  category: 'application' | 'documents' | 'preparation' | 'tips';\n  slug: string;\n  thumbnail?: string;\n  readTime?: number;\n  tags?: string[];\n  createdAt: string;\n}\n\nconst Guides: React.FC = () => {\n  const { translations } = useLanguage();\n  const [guides, setGuides] = useState<Guide[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    fetchGuides();\n  }, [selectedCategory]);\n\n  const fetchGuides = async () => {\n    try {\n      const params = new URLSearchParams({\n        published: 'true',\n        orderBy: 'created_at',\n        orderDirection: 'DESC'\n      });\n\n      if (selectedCategory) {\n        params.append('category', selectedCategory);\n      }\n\n      const response = await fetch(`/api/guides?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setGuides(data.data.guides || []);\n      } else {\n        console.error('Failed to fetch guides');\n      }\n    } catch (error) {\n      console.error('Error fetching guides:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = async () => {\n    if (searchTerm.trim() === '') {\n      fetchGuides();\n      return;\n    }\n\n    try {\n      const params = new URLSearchParams({\n        q: searchTerm,\n        category: selectedCategory\n      });\n\n      const response = await fetch(`/api/guides/search?${params}`);\n      if (response.ok) {\n        const data = await response.json();\n        setGuides(data.data.guides || []);\n      }\n    } catch (error) {\n      console.error('Error searching guides:', error);\n    }\n  };\n\n  const getCategoryIcon = (category: string): string => {\n    const icons = {\n      application: '📝',\n      documents: '📄',\n      preparation: '🎯',\n      tips: '💡'\n    };\n    return icons[category as keyof typeof icons] || '📚';\n  };\n\n  const getCategoryColor = (category: string): string => {\n    const colors = {\n      application: 'bg-blue-100 text-blue-800',\n      documents: 'bg-green-100 text-green-800',\n      preparation: 'bg-purple-100 text-purple-800',\n      tips: 'bg-yellow-100 text-yellow-800'\n    };\n    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';\n  };\n\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Chargement des guides...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">\n              {translations.guides.title}\n            </h1>\n            <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n              {translations.guides.subtitle}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"md:col-span-2\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Rechercher dans les guides...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                  className=\"w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n            \n            <div>\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">Toutes les catégories</option>\n                <option value=\"application\">{translations.guides.categories.application}</option>\n                <option value=\"documents\">{translations.guides.categories.documents}</option>\n                <option value=\"preparation\">{translations.guides.categories.preparation}</option>\n                <option value=\"tips\">{translations.guides.categories.tips}</option>\n              </select>\n            </div>\n          </div>\n          \n          <div className=\"mt-4 flex justify-center\">\n            <button\n              onClick={handleSearch}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n            >\n              Rechercher\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Category Quick Links */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\">\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n          {Object.entries(translations.guides.categories).map(([key, label]) => (\n            <button\n              key={key}\n              onClick={() => setSelectedCategory(selectedCategory === key ? '' : key)}\n              className={`p-4 rounded-lg border-2 transition-all duration-200 ${\n                selectedCategory === key\n                  ? 'border-blue-500 bg-blue-50 text-blue-700'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"text-2xl mb-2\">{getCategoryIcon(key)}</div>\n              <div className=\"font-medium\">{label}</div>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Guides Grid */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        {guides.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">📚</div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              Aucun guide trouvé\n            </h3>\n            <p className=\"text-gray-600\">\n              Essayez de modifier votre recherche ou parcourez toutes les catégories.\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {guides.map((guide) => (\n              <Link\n                key={guide.id}\n                to={`/guides/${guide.slug}`}\n                className=\"group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200\"\n              >\n                {guide.thumbnail && (\n                  <div className=\"aspect-w-16 aspect-h-9 overflow-hidden\">\n                    <img\n                      src={guide.thumbnail}\n                      alt={guide.title}\n                      className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  </div>\n                )}\n                \n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(guide.category)}`}>\n                      <span className=\"mr-1\">{getCategoryIcon(guide.category)}</span>\n                      {translations.guides.categories[guide.category]}\n                    </span>\n                    \n                    {guide.readTime && (\n                      <span className=\"text-sm text-gray-500\">\n                        {guide.readTime} min\n                      </span>\n                    )}\n                  </div>\n                  \n                  <h3 className=\"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-2 line-clamp-2\">\n                    {guide.title}\n                  </h3>\n                  \n                  {guide.excerpt && (\n                    <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">\n                      {guide.excerpt}\n                    </p>\n                  )}\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-xs text-gray-500\">\n                      {formatDate(guide.createdAt)}\n                    </span>\n                    \n                    <div className=\"flex items-center text-blue-600 text-sm font-medium\">\n                      <span>{translations.guides.readMore}</span>\n                      <svg className=\"ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </div>\n                  </div>\n                  \n                  {guide.tags && guide.tags.length > 0 && (\n                    <div className=\"mt-4 flex flex-wrap gap-1\">\n                      {guide.tags.slice(0, 3).map((tag, index) => (\n                        <span\n                          key={index}\n                          className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\"\n                        >\n                          #{tag}\n                        </span>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </Link>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Guides;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAczD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAa,CAAC,GAAGL,WAAW,CAAC,CAAC;EACtC,MAAM,CAACM,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdgB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACJ,gBAAgB,CAAC,CAAC;EAEtB,MAAMI,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,YAAY;QACrBC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAIT,gBAAgB,EAAE;QACpBK,MAAM,CAACK,MAAM,CAAC,UAAU,EAAEV,gBAAgB,CAAC;MAC7C;MAEA,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,eAAeP,MAAM,EAAE,CAAC;MACrD,IAAIM,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClClB,SAAS,CAACiB,IAAI,CAACA,IAAI,CAAClB,MAAM,IAAI,EAAE,CAAC;MACnC,CAAC,MAAM;QACLoB,OAAO,CAACC,KAAK,CAAC,wBAAwB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIhB,UAAU,CAACiB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5Bf,WAAW,CAAC,CAAC;MACb;IACF;IAEA,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCc,CAAC,EAAElB,UAAU;QACbmB,QAAQ,EAAErB;MACZ,CAAC,CAAC;MAEF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,sBAAsBP,MAAM,EAAE,CAAC;MAC5D,IAAIM,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClClB,SAAS,CAACiB,IAAI,CAACA,IAAI,CAAClB,MAAM,IAAI,EAAE,CAAC;MACnC;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMK,eAAe,GAAID,QAAgB,IAAa;IACpD,MAAME,KAAK,GAAG;MACZC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE;IACR,CAAC;IACD,OAAOJ,KAAK,CAACF,QAAQ,CAAuB,IAAI,IAAI;EACtD,CAAC;EAED,MAAMO,gBAAgB,GAAIP,QAAgB,IAAa;IACrD,MAAMQ,MAAM,GAAG;MACbL,WAAW,EAAE,2BAA2B;MACxCC,SAAS,EAAE,6BAA6B;MACxCC,WAAW,EAAE,+BAA+B;MAC5CC,IAAI,EAAE;IACR,CAAC;IACD,OAAOE,MAAM,CAACR,QAAQ,CAAwB,IAAI,2BAA2B;EAC/E,CAAC;EAED,MAAMS,UAAU,GAAIC,UAAkB,IAAa;IACjD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,IAAIvC,OAAO,EAAE;IACX,oBACEN,OAAA;MAAK8C,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9E/C,OAAA;QAAK8C,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D/C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/C,OAAA;YAAK8C,SAAS,EAAC;UAAwE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9FnD,OAAA;YAAG8C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnD,OAAA;IAAK8C,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExE/C,OAAA;MAAK8C,SAAS,EAAC,+DAA+D;MAAAC,QAAA,eAC5E/C,OAAA;QAAK8C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD/C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/C,OAAA;YAAI8C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAChD5C,YAAY,CAACC,MAAM,CAACgD;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACLnD,OAAA;YAAG8C,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACnD5C,YAAY,CAACC,MAAM,CAACiD;UAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D/C,OAAA;QAAK8C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD/C,OAAA;UAAK8C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/C,OAAA;cAAK8C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/C,OAAA;gBACEsD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,+BAA+B;gBAC3CC,KAAK,EAAE9C,UAAW;gBAClB+C,QAAQ,EAAGC,CAAC,IAAK/C,aAAa,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CI,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAInC,YAAY,CAAC,CAAE;gBACvDoB,SAAS,EAAC;cAAoH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H,CAAC,eACFnD,OAAA;gBAAK8C,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF/C,OAAA;kBAAK8C,SAAS,EAAC,uBAAuB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAC1F/C,OAAA;oBAAMiE,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA6C;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnD,OAAA;YAAA+C,QAAA,eACE/C,OAAA;cACEwD,KAAK,EAAEhD,gBAAiB;cACxBiD,QAAQ,EAAGC,CAAC,IAAKjD,mBAAmB,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDV,SAAS,EAAC,8GAA8G;cAAAC,QAAA,gBAExH/C,OAAA;gBAAQwD,KAAK,EAAC,EAAE;gBAAAT,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/CnD,OAAA;gBAAQwD,KAAK,EAAC,aAAa;gBAAAT,QAAA,EAAE5C,YAAY,CAACC,MAAM,CAACiE,UAAU,CAACrC;cAAW;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACjFnD,OAAA;gBAAQwD,KAAK,EAAC,WAAW;gBAAAT,QAAA,EAAE5C,YAAY,CAACC,MAAM,CAACiE,UAAU,CAACpC;cAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC7EnD,OAAA;gBAAQwD,KAAK,EAAC,aAAa;gBAAAT,QAAA,EAAE5C,YAAY,CAACC,MAAM,CAACiE,UAAU,CAACnC;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACjFnD,OAAA;gBAAQwD,KAAK,EAAC,MAAM;gBAAAT,QAAA,EAAE5C,YAAY,CAACC,MAAM,CAACiE,UAAU,CAAClC;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAK8C,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvC/C,OAAA;YACEsE,OAAO,EAAE5C,YAAa;YACtBoB,SAAS,EAAC,8FAA8F;YAAAC,QAAA,EACzG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC1D/C,OAAA;QAAK8C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDwB,MAAM,CAACC,OAAO,CAACrE,YAAY,CAACC,MAAM,CAACiE,UAAU,CAAC,CAACI,GAAG,CAAC,CAAC,CAACZ,GAAG,EAAEa,KAAK,CAAC,kBAC/D1E,OAAA;UAEEsE,OAAO,EAAEA,CAAA,KAAM7D,mBAAmB,CAACD,gBAAgB,KAAKqD,GAAG,GAAG,EAAE,GAAGA,GAAG,CAAE;UACxEf,SAAS,EAAE,uDACTtC,gBAAgB,KAAKqD,GAAG,GACpB,0CAA0C,GAC1C,iEAAiE,EACpE;UAAAd,QAAA,gBAEH/C,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEjB,eAAe,CAAC+B,GAAG;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DnD,OAAA;YAAK8C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE2B;UAAK;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GATrCU,GAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUF,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAK8C,SAAS,EAAC,8CAA8C;MAAAC,QAAA,EAC1D3C,MAAM,CAACuE,MAAM,KAAK,CAAC,gBAClB3E,OAAA;QAAK8C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/C,OAAA;UAAK8C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCnD,OAAA;UAAI8C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnD,OAAA;UAAG8C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENnD,OAAA;QAAK8C,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE3C,MAAM,CAACqE,GAAG,CAAEG,KAAK,iBAChB5E,OAAA,CAACH,IAAI;UAEHgF,EAAE,EAAE,WAAWD,KAAK,CAACE,IAAI,EAAG;UAC5BhC,SAAS,EAAC,8IAA8I;UAAAC,QAAA,GAEvJ6B,KAAK,CAACG,SAAS,iBACd/E,OAAA;YAAK8C,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrD/C,OAAA;cACEgF,GAAG,EAAEJ,KAAK,CAACG,SAAU;cACrBE,GAAG,EAAEL,KAAK,CAACxB,KAAM;cACjBN,SAAS,EAAC;YAAkF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDnD,OAAA;YAAK8C,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB/C,OAAA;cAAK8C,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrD/C,OAAA;gBAAM8C,SAAS,EAAE,2EAA2EV,gBAAgB,CAACwC,KAAK,CAAC/C,QAAQ,CAAC,EAAG;gBAAAkB,QAAA,gBAC7H/C,OAAA;kBAAM8C,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEjB,eAAe,CAAC8C,KAAK,CAAC/C,QAAQ;gBAAC;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC9DhD,YAAY,CAACC,MAAM,CAACiE,UAAU,CAACO,KAAK,CAAC/C,QAAQ,CAAC;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EAENyB,KAAK,CAACM,QAAQ,iBACblF,OAAA;gBAAM8C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACpC6B,KAAK,CAACM,QAAQ,EAAC,MAClB;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENnD,OAAA;cAAI8C,SAAS,EAAC,gHAAgH;cAAAC,QAAA,EAC3H6B,KAAK,CAACxB;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAEJyB,KAAK,CAACO,OAAO,iBACZnF,OAAA;cAAG8C,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EACnD6B,KAAK,CAACO;YAAO;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACJ,eAEDnD,OAAA;cAAK8C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD/C,OAAA;gBAAM8C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpCT,UAAU,CAACsC,KAAK,CAACQ,SAAS;cAAC;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAEPnD,OAAA;gBAAK8C,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,gBAClE/C,OAAA;kBAAA+C,QAAA,EAAO5C,YAAY,CAACC,MAAM,CAACiF;gBAAQ;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3CnD,OAAA;kBAAK8C,SAAS,EAAC,0EAA0E;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAC7I/C,OAAA;oBAAMiE,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELyB,KAAK,CAACU,IAAI,IAAIV,KAAK,CAACU,IAAI,CAACX,MAAM,GAAG,CAAC,iBAClC3E,OAAA;cAAK8C,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EACvC6B,KAAK,CAACU,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACd,GAAG,CAAC,CAACe,GAAG,EAAEC,KAAK,kBACrCzF,OAAA;gBAEE8C,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,GAC7E,GACE,EAACyC,GAAG;cAAA,GAHAC,KAAK;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIN,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA/DDyB,KAAK,CAACc,EAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgET,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CAhRID,MAAgB;EAAA,QACKH,WAAW;AAAA;AAAA6F,EAAA,GADhC1F,MAAgB;AAkRtB,eAAeA,MAAM;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}