{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';\nimport { Button, Table, Space, Modal, Form, Input, Select, Switch, InputNumber, Tag, message } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst GuideManager = () => {\n  _s();\n  const [guides, setGuides] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingGuide, setEditingGuide] = useState(null);\n  const [form] = Form.useForm();\n  useEffect(() => {\n    fetchGuides();\n  }, []);\n  const fetchGuides = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/guides?limit=100', {\n        credentials: 'include'\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setGuides(data.data.guides || []);\n      } else {\n        message.error('Failed to fetch guides');\n      }\n    } catch (error) {\n      console.error('Error fetching guides:', error);\n      message.error('Error fetching guides');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreate = () => {\n    setEditingGuide(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = guide => {\n    var _guide$tags;\n    setEditingGuide(guide);\n    form.setFieldsValue({\n      ...guide,\n      tags: ((_guide$tags = guide.tags) === null || _guide$tags === void 0 ? void 0 : _guide$tags.join(', ')) || ''\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    Modal.confirm({\n      title: 'Delete Guide',\n      content: 'Are you sure you want to delete this guide? This action cannot be undone.',\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          const response = await fetch(`/api/guides/${id}`, {\n            method: 'DELETE',\n            credentials: 'include'\n          });\n          if (response.ok) {\n            message.success('Guide deleted successfully');\n            fetchGuides();\n          } else {\n            message.error('Failed to delete guide');\n          }\n        } catch (error) {\n          console.error('Error deleting guide:', error);\n          message.error('Error deleting guide');\n        }\n      }\n    });\n  };\n  const handleSubmit = async values => {\n    try {\n      const guideData = {\n        ...values,\n        tags: values.tags ? values.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],\n        slug: values.slug || values.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')\n      };\n      const url = editingGuide ? `/api/guides/${editingGuide.id}` : '/api/guides';\n      const method = editingGuide ? 'PUT' : 'POST';\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(guideData)\n      });\n      if (response.ok) {\n        message.success(`Guide ${editingGuide ? 'updated' : 'created'} successfully`);\n        setModalVisible(false);\n        fetchGuides();\n      } else {\n        const errorData = await response.json();\n        message.error(errorData.message || `Failed to ${editingGuide ? 'update' : 'create'} guide`);\n      }\n    } catch (error) {\n      console.error('Error saving guide:', error);\n      message.error('Error saving guide');\n    }\n  };\n  const getCategoryColor = category => {\n    const colors = {\n      application: 'blue',\n      documents: 'green',\n      preparation: 'purple',\n      tips: 'orange'\n    };\n    return colors[category] || 'default';\n  };\n  const getCategoryLabel = category => {\n    const labels = {\n      application: 'Application',\n      documents: 'Documents',\n      preparation: 'Preparation',\n      tips: 'Tips'\n    };\n    return labels[category] || category;\n  };\n  const columns = [{\n    title: 'Title',\n    dataIndex: 'title',\n    key: 'title',\n    width: '25%',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [\"/\", record.slug]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Category',\n    dataIndex: 'category',\n    key: 'category',\n    width: '15%',\n    render: category => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getCategoryColor(category),\n      children: getCategoryLabel(category)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Status',\n    dataIndex: 'isPublished',\n    key: 'isPublished',\n    width: '10%',\n    render: isPublished => /*#__PURE__*/_jsxDEV(Tag, {\n      color: isPublished ? 'green' : 'red',\n      children: isPublished ? 'Published' : 'Draft'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Read Time',\n    dataIndex: 'readTime',\n    key: 'readTime',\n    width: '10%',\n    render: readTime => readTime ? `${readTime} min` : '-'\n  }, {\n    title: 'Tags',\n    dataIndex: 'tags',\n    key: 'tags',\n    width: '20%',\n    render: tags => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [tags === null || tags === void 0 ? void 0 : tags.slice(0, 3).map((tag, index) => /*#__PURE__*/_jsxDEV(Tag, {\n        className: \"text-xs\",\n        children: tag\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 13\n      }, this)), (tags === null || tags === void 0 ? void 0 : tags.length) > 3 && /*#__PURE__*/_jsxDEV(Tag, {\n        className: \"text-xs\",\n        children: [\"+\", tags.length - 3]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Created',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: '10%',\n    render: date => new Date(date).toLocaleDateString()\n  }, {\n    title: 'Actions',\n    key: 'actions',\n    width: '10%',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 19\n        }, this),\n        onClick: () => window.open(`/guides/${record.slug}`, '_blank'),\n        title: \"View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        title: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        title: \"Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Guide Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage guides and educational content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 17\n        }, this),\n        onClick: handleCreate,\n        size: \"large\",\n        children: \"Create Guide\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: guides,\n      rowKey: \"id\",\n      loading: loading,\n      pagination: {\n        pageSize: 10,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: total => `Total ${total} guides`\n      },\n      scroll: {\n        x: 1200\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingGuide ? 'Edit Guide' : 'Create Guide',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      footer: null,\n      width: 800,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          isPublished: true,\n          category: 'application'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"Title\",\n          rules: [{\n            required: true,\n            message: 'Please enter the title'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"Enter guide title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"slug\",\n          label: \"URL Slug\",\n          help: \"Leave empty to auto-generate from title\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"url-friendly-slug\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"category\",\n          label: \"Category\",\n          rules: [{\n            required: true,\n            message: 'Please select a category'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"Select category\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"application\",\n              children: \"Application\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"documents\",\n              children: \"Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"preparation\",\n              children: \"Preparation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"tips\",\n              children: \"Tips\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"excerpt\",\n          label: \"Excerpt\",\n          help: \"Brief description for preview\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"Brief description of the guide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"content\",\n          label: \"Content\",\n          rules: [{\n            required: true,\n            message: 'Please enter the content'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 10,\n            placeholder: \"Guide content (Markdown supported)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"readTime\",\n            label: \"Read Time (minutes)\",\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              max: 120,\n              placeholder: \"5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"thumbnail\",\n            label: \"Thumbnail URL\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"https://example.com/image.jpg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"tags\",\n          label: \"Tags\",\n          help: \"Comma-separated tags\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"cv, application, tips\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"isPublished\",\n          label: \"Status\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Switch, {\n            checkedChildren: \"Published\",\n            unCheckedChildren: \"Draft\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-2 pt-4 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setModalVisible(false),\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: [editingGuide ? 'Update' : 'Create', \" Guide\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this);\n};\n_s(GuideManager, \"lcEaMUljNrjdoLS6XybWL/HEkDo=\", false, function () {\n  return [Form.useForm];\n});\n_c = GuideManager;\nexport default GuideManager;\nvar _c;\n$RefreshReg$(_c, \"GuideManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "<PERSON><PERSON>", "Table", "Space", "Modal", "Form", "Input", "Select", "Switch", "InputNumber", "Tag", "message", "jsxDEV", "_jsxDEV", "TextArea", "Option", "GuideManager", "_s", "guides", "setGuides", "loading", "setLoading", "modalVisible", "setModalVisible", "editingGuide", "setEditingGuide", "form", "useForm", "fetchGuides", "response", "fetch", "credentials", "ok", "data", "json", "error", "console", "handleCreate", "resetFields", "handleEdit", "guide", "_guide$tags", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tags", "join", "handleDelete", "id", "confirm", "title", "content", "okText", "okType", "cancelText", "onOk", "method", "success", "handleSubmit", "values", "guideData", "split", "map", "tag", "trim", "filter", "Boolean", "slug", "toLowerCase", "replace", "url", "headers", "body", "JSON", "stringify", "errorData", "getCategoryColor", "category", "colors", "application", "documents", "preparation", "tips", "getCategoryLabel", "labels", "columns", "dataIndex", "key", "width", "render", "text", "record", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "isPublished", "readTime", "slice", "index", "length", "date", "Date", "toLocaleDateString", "_", "size", "type", "icon", "onClick", "window", "open", "danger", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "onCancel", "footer", "destroyOnClose", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "help", "value", "rows", "min", "max", "valuePropName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';\nimport { Button, Table, Space, Modal, Form, Input, Select, Switch, InputNumber, Tag, message } from 'antd';\n\nconst { TextArea } = Input;\nconst { Option } = Select;\n\ninterface Guide {\n  id: number;\n  title: string;\n  content: string;\n  category: 'application' | 'documents' | 'preparation' | 'tips';\n  slug: string;\n  excerpt?: string;\n  thumbnail?: string;\n  isPublished: boolean;\n  readTime?: number;\n  tags?: string[];\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst GuideManager: React.FC = () => {\n  const [guides, setGuides] = useState<Guide[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingGuide, setEditingGuide] = useState<Guide | null>(null);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchGuides();\n  }, []);\n\n  const fetchGuides = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/guides?limit=100', {\n        credentials: 'include'\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setGuides(data.data.guides || []);\n      } else {\n        message.error('Failed to fetch guides');\n      }\n    } catch (error) {\n      console.error('Error fetching guides:', error);\n      message.error('Error fetching guides');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreate = () => {\n    setEditingGuide(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (guide: Guide) => {\n    setEditingGuide(guide);\n    form.setFieldsValue({\n      ...guide,\n      tags: guide.tags?.join(', ') || ''\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    Modal.confirm({\n      title: 'Delete Guide',\n      content: 'Are you sure you want to delete this guide? This action cannot be undone.',\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          const response = await fetch(`/api/guides/${id}`, {\n            method: 'DELETE',\n            credentials: 'include'\n          });\n          if (response.ok) {\n            message.success('Guide deleted successfully');\n            fetchGuides();\n          } else {\n            message.error('Failed to delete guide');\n          }\n        } catch (error) {\n          console.error('Error deleting guide:', error);\n          message.error('Error deleting guide');\n        }\n      }\n    });\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      const guideData = {\n        ...values,\n        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],\n        slug: values.slug || values.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '')\n      };\n\n      const url = editingGuide ? `/api/guides/${editingGuide.id}` : '/api/guides';\n      const method = editingGuide ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(guideData)\n      });\n\n      if (response.ok) {\n        message.success(`Guide ${editingGuide ? 'updated' : 'created'} successfully`);\n        setModalVisible(false);\n        fetchGuides();\n      } else {\n        const errorData = await response.json();\n        message.error(errorData.message || `Failed to ${editingGuide ? 'update' : 'create'} guide`);\n      }\n    } catch (error) {\n      console.error('Error saving guide:', error);\n      message.error('Error saving guide');\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    const colors = {\n      application: 'blue',\n      documents: 'green',\n      preparation: 'purple',\n      tips: 'orange'\n    };\n    return colors[category as keyof typeof colors] || 'default';\n  };\n\n  const getCategoryLabel = (category: string) => {\n    const labels = {\n      application: 'Application',\n      documents: 'Documents',\n      preparation: 'Preparation',\n      tips: 'Tips'\n    };\n    return labels[category as keyof typeof labels] || category;\n  };\n\n  const columns = [\n    {\n      title: 'Title',\n      dataIndex: 'title',\n      key: 'title',\n      width: '25%',\n      render: (text: string, record: Guide) => (\n        <div>\n          <div className=\"font-medium\">{text}</div>\n          <div className=\"text-sm text-gray-500\">/{record.slug}</div>\n        </div>\n      )\n    },\n    {\n      title: 'Category',\n      dataIndex: 'category',\n      key: 'category',\n      width: '15%',\n      render: (category: string) => (\n        <Tag color={getCategoryColor(category)}>\n          {getCategoryLabel(category)}\n        </Tag>\n      )\n    },\n    {\n      title: 'Status',\n      dataIndex: 'isPublished',\n      key: 'isPublished',\n      width: '10%',\n      render: (isPublished: boolean) => (\n        <Tag color={isPublished ? 'green' : 'red'}>\n          {isPublished ? 'Published' : 'Draft'}\n        </Tag>\n      )\n    },\n    {\n      title: 'Read Time',\n      dataIndex: 'readTime',\n      key: 'readTime',\n      width: '10%',\n      render: (readTime: number) => readTime ? `${readTime} min` : '-'\n    },\n    {\n      title: 'Tags',\n      dataIndex: 'tags',\n      key: 'tags',\n      width: '20%',\n      render: (tags: string[]) => (\n        <div>\n          {tags?.slice(0, 3).map((tag, index) => (\n            <Tag key={index} className=\"text-xs\">{tag}</Tag>\n          ))}\n          {tags?.length > 3 && <Tag className=\"text-xs\">+{tags.length - 3}</Tag>}\n        </div>\n      )\n    },\n    {\n      title: 'Created',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: '10%',\n      render: (date: string) => new Date(date).toLocaleDateString()\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      width: '10%',\n      render: (_: any, record: Guide) => (\n        <Space size=\"small\">\n          <Button\n            type=\"text\"\n            icon={<EyeOutlined />}\n            onClick={() => window.open(`/guides/${record.slug}`, '_blank')}\n            title=\"View\"\n          />\n          <Button\n            type=\"text\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n            title=\"Edit\"\n          />\n          <Button\n            type=\"text\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n            title=\"Delete\"\n          />\n        </Space>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Guide Management</h1>\n          <p className=\"text-gray-600\">Manage guides and educational content</p>\n        </div>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleCreate}\n          size=\"large\"\n        >\n          Create Guide\n        </Button>\n      </div>\n\n      <Table\n        columns={columns}\n        dataSource={guides}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `Total ${total} guides`\n        }}\n        scroll={{ x: 1200 }}\n      />\n\n      <Modal\n        title={editingGuide ? 'Edit Guide' : 'Create Guide'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={null}\n        width={800}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            isPublished: true,\n            category: 'application'\n          }}\n        >\n          <Form.Item\n            name=\"title\"\n            label=\"Title\"\n            rules={[{ required: true, message: 'Please enter the title' }]}\n          >\n            <Input placeholder=\"Enter guide title\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"slug\"\n            label=\"URL Slug\"\n            help=\"Leave empty to auto-generate from title\"\n          >\n            <Input placeholder=\"url-friendly-slug\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"category\"\n            label=\"Category\"\n            rules={[{ required: true, message: 'Please select a category' }]}\n          >\n            <Select placeholder=\"Select category\">\n              <Option value=\"application\">Application</Option>\n              <Option value=\"documents\">Documents</Option>\n              <Option value=\"preparation\">Preparation</Option>\n              <Option value=\"tips\">Tips</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"excerpt\"\n            label=\"Excerpt\"\n            help=\"Brief description for preview\"\n          >\n            <TextArea rows={2} placeholder=\"Brief description of the guide\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"content\"\n            label=\"Content\"\n            rules={[{ required: true, message: 'Please enter the content' }]}\n          >\n            <TextArea rows={10} placeholder=\"Guide content (Markdown supported)\" />\n          </Form.Item>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"readTime\"\n              label=\"Read Time (minutes)\"\n            >\n              <InputNumber min={1} max={120} placeholder=\"5\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"thumbnail\"\n              label=\"Thumbnail URL\"\n            >\n              <Input placeholder=\"https://example.com/image.jpg\" />\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"tags\"\n            label=\"Tags\"\n            help=\"Comma-separated tags\"\n          >\n            <Input placeholder=\"cv, application, tips\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"isPublished\"\n            label=\"Status\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"Published\" unCheckedChildren=\"Draft\" />\n          </Form.Item>\n\n          <div className=\"flex justify-end space-x-2 pt-4 border-t\">\n            <Button onClick={() => setModalVisible(false)}>\n              Cancel\n            </Button>\n            <Button type=\"primary\" htmlType=\"submit\">\n              {editingGuide ? 'Update' : 'Create'} Guide\n            </Button>\n          </div>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default GuideManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAC3F,SAASC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3G,MAAM;EAAEC;AAAS,CAAC,GAAGR,KAAK;AAC1B,MAAM;EAAES;AAAO,CAAC,GAAGR,MAAM;AAiBzB,MAAMS,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAe,IAAI,CAAC;EACpE,MAAM,CAAC+B,IAAI,CAAC,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAE7B/B,SAAS,CAAC,MAAM;IACdgC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,uBAAuB,EAAE;QACpDC,WAAW,EAAE;MACf,CAAC,CAAC;MACF,IAAIF,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCf,SAAS,CAACc,IAAI,CAACA,IAAI,CAACf,MAAM,IAAI,EAAE,CAAC;MACnC,CAAC,MAAM;QACLP,OAAO,CAACwB,KAAK,CAAC,wBAAwB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CxB,OAAO,CAACwB,KAAK,CAAC,uBAAuB,CAAC;IACxC,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzBZ,eAAe,CAAC,IAAI,CAAC;IACrBC,IAAI,CAACY,WAAW,CAAC,CAAC;IAClBf,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgB,UAAU,GAAIC,KAAY,IAAK;IAAA,IAAAC,WAAA;IACnChB,eAAe,CAACe,KAAK,CAAC;IACtBd,IAAI,CAACgB,cAAc,CAAC;MAClB,GAAGF,KAAK;MACRG,IAAI,EAAE,EAAAF,WAAA,GAAAD,KAAK,CAACG,IAAI,cAAAF,WAAA,uBAAVA,WAAA,CAAYG,IAAI,CAAC,IAAI,CAAC,KAAI;IAClC,CAAC,CAAC;IACFrB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMsB,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC1C,KAAK,CAAC2C,OAAO,CAAC;MACZC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAE,2EAA2E;MACpFC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,QAAQ;MACpBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAMxB,QAAQ,GAAG,MAAMC,KAAK,CAAC,eAAegB,EAAE,EAAE,EAAE;YAChDQ,MAAM,EAAE,QAAQ;YAChBvB,WAAW,EAAE;UACf,CAAC,CAAC;UACF,IAAIF,QAAQ,CAACG,EAAE,EAAE;YACfrB,OAAO,CAAC4C,OAAO,CAAC,4BAA4B,CAAC;YAC7C3B,WAAW,CAAC,CAAC;UACf,CAAC,MAAM;YACLjB,OAAO,CAACwB,KAAK,CAAC,wBAAwB,CAAC;UACzC;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CxB,OAAO,CAACwB,KAAK,CAAC,sBAAsB,CAAC;QACvC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,MAAMC,SAAS,GAAG;QAChB,GAAGD,MAAM;QACTd,IAAI,EAAEc,MAAM,CAACd,IAAI,GAAGc,MAAM,CAACd,IAAI,CAACgB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,GAAW,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;QAChGC,IAAI,EAAER,MAAM,CAACQ,IAAI,IAAIR,MAAM,CAACT,KAAK,CAACkB,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,EAAE;MAClG,CAAC;MAED,MAAMC,GAAG,GAAG5C,YAAY,GAAG,eAAeA,YAAY,CAACsB,EAAE,EAAE,GAAG,aAAa;MAC3E,MAAMQ,MAAM,GAAG9B,YAAY,GAAG,KAAK,GAAG,MAAM;MAE5C,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACsC,GAAG,EAAE;QAChCd,MAAM;QACNe,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDtC,WAAW,EAAE,SAAS;QACtBuC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACd,SAAS;MAChC,CAAC,CAAC;MAEF,IAAI7B,QAAQ,CAACG,EAAE,EAAE;QACfrB,OAAO,CAAC4C,OAAO,CAAC,SAAS/B,YAAY,GAAG,SAAS,GAAG,SAAS,eAAe,CAAC;QAC7ED,eAAe,CAAC,KAAK,CAAC;QACtBK,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACL,MAAM6C,SAAS,GAAG,MAAM5C,QAAQ,CAACK,IAAI,CAAC,CAAC;QACvCvB,OAAO,CAACwB,KAAK,CAACsC,SAAS,CAAC9D,OAAO,IAAI,aAAaa,YAAY,GAAG,QAAQ,GAAG,QAAQ,QAAQ,CAAC;MAC7F;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CxB,OAAO,CAACwB,KAAK,CAAC,oBAAoB,CAAC;IACrC;EACF,CAAC;EAED,MAAMuC,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,MAAMC,MAAM,GAAG;MACbC,WAAW,EAAE,MAAM;MACnBC,SAAS,EAAE,OAAO;MAClBC,WAAW,EAAE,QAAQ;MACrBC,IAAI,EAAE;IACR,CAAC;IACD,OAAOJ,MAAM,CAACD,QAAQ,CAAwB,IAAI,SAAS;EAC7D,CAAC;EAED,MAAMM,gBAAgB,GAAIN,QAAgB,IAAK;IAC7C,MAAMO,MAAM,GAAG;MACbL,WAAW,EAAE,aAAa;MAC1BC,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,aAAa;MAC1BC,IAAI,EAAE;IACR,CAAC;IACD,OAAOE,MAAM,CAACP,QAAQ,CAAwB,IAAIA,QAAQ;EAC5D,CAAC;EAED,MAAMQ,OAAO,GAAG,CACd;IACEnC,KAAK,EAAE,OAAO;IACdoC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACC,IAAY,EAAEC,MAAa,kBAClC5E,OAAA;MAAA6E,QAAA,gBACE7E,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzClF,OAAA;QAAK8E,SAAS,EAAC,uBAAuB;QAAAD,QAAA,GAAC,GAAC,EAACD,MAAM,CAACxB,IAAI;MAAA;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAET,CAAC,EACD;IACE/C,KAAK,EAAE,UAAU;IACjBoC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGZ,QAAgB,iBACvB9D,OAAA,CAACH,GAAG;MAACsF,KAAK,EAAEtB,gBAAgB,CAACC,QAAQ,CAAE;MAAAe,QAAA,EACpCT,gBAAgB,CAACN,QAAQ;IAAC;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAET,CAAC,EACD;IACE/C,KAAK,EAAE,QAAQ;IACfoC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGU,WAAoB,iBAC3BpF,OAAA,CAACH,GAAG;MAACsF,KAAK,EAAEC,WAAW,GAAG,OAAO,GAAG,KAAM;MAAAP,QAAA,EACvCO,WAAW,GAAG,WAAW,GAAG;IAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAET,CAAC,EACD;IACE/C,KAAK,EAAE,WAAW;IAClBoC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGW,QAAgB,IAAKA,QAAQ,GAAG,GAAGA,QAAQ,MAAM,GAAG;EAC/D,CAAC,EACD;IACElD,KAAK,EAAE,MAAM;IACboC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAG5C,IAAc,iBACrB9B,OAAA;MAAA6E,QAAA,GACG/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACvC,GAAG,CAAC,CAACC,GAAG,EAAEuC,KAAK,kBAChCvF,OAAA,CAACH,GAAG;QAAaiF,SAAS,EAAC,SAAS;QAAAD,QAAA,EAAE7B;MAAG,GAA/BuC,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgC,CAChD,CAAC,EACD,CAAApD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,MAAM,IAAG,CAAC,iBAAIxF,OAAA,CAACH,GAAG;QAACiF,SAAS,EAAC,SAAS;QAAAD,QAAA,GAAC,GAAC,EAAC/C,IAAI,CAAC0D,MAAM,GAAG,CAAC;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE;EAET,CAAC,EACD;IACE/C,KAAK,EAAE,SAAS;IAChBoC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGe,IAAY,IAAK,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC;EAC9D,CAAC,EACD;IACExD,KAAK,EAAE,SAAS;IAChBqC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACkB,CAAM,EAAEhB,MAAa,kBAC5B5E,OAAA,CAACV,KAAK;MAACuG,IAAI,EAAC,OAAO;MAAAhB,QAAA,gBACjB7E,OAAA,CAACZ,MAAM;QACL0G,IAAI,EAAC,MAAM;QACXC,IAAI,eAAE/F,OAAA,CAACb,WAAW;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBc,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAAC,WAAWtB,MAAM,CAACxB,IAAI,EAAE,EAAE,QAAQ,CAAE;QAC/DjB,KAAK,EAAC;MAAM;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACFlF,OAAA,CAACZ,MAAM;QACL0G,IAAI,EAAC,MAAM;QACXC,IAAI,eAAE/F,OAAA,CAACf,YAAY;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBc,OAAO,EAAEA,CAAA,KAAMtE,UAAU,CAACkD,MAAM,CAAE;QAClCzC,KAAK,EAAC;MAAM;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACFlF,OAAA,CAACZ,MAAM;QACL0G,IAAI,EAAC,MAAM;QACXK,MAAM;QACNJ,IAAI,eAAE/F,OAAA,CAACd,cAAc;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBc,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAAC4C,MAAM,CAAC3C,EAAE,CAAE;QACvCE,KAAK,EAAC;MAAQ;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAEX,CAAC,CACF;EAED,oBACElF,OAAA;IAAK8E,SAAS,EAAC,KAAK;IAAAD,QAAA,gBAClB7E,OAAA;MAAK8E,SAAS,EAAC,wCAAwC;MAAAD,QAAA,gBACrD7E,OAAA;QAAA6E,QAAA,gBACE7E,OAAA;UAAI8E,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtElF,OAAA;UAAG8E,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACNlF,OAAA,CAACZ,MAAM;QACL0G,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE/F,OAAA,CAAChB,YAAY;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBc,OAAO,EAAExE,YAAa;QACtBqE,IAAI,EAAC,OAAO;QAAAhB,QAAA,EACb;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlF,OAAA,CAACX,KAAK;MACJiF,OAAO,EAAEA,OAAQ;MACjB8B,UAAU,EAAE/F,MAAO;MACnBgG,MAAM,EAAC,IAAI;MACX9F,OAAO,EAAEA,OAAQ;MACjB+F,UAAU,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,IAAI;QACrBC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAGC,KAAK,IAAK,SAASA,KAAK;MACtC,CAAE;MACFC,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK;IAAE;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEFlF,OAAA,CAACT,KAAK;MACJ4C,KAAK,EAAExB,YAAY,GAAG,YAAY,GAAG,cAAe;MACpDuF,IAAI,EAAEzF,YAAa;MACnBqG,QAAQ,EAAEA,CAAA,KAAMpG,eAAe,CAAC,KAAK,CAAE;MACvCqG,MAAM,EAAE,IAAK;MACbtC,KAAK,EAAE,GAAI;MACXuC,cAAc;MAAAnC,QAAA,eAEd7E,OAAA,CAACR,IAAI;QACHqB,IAAI,EAAEA,IAAK;QACXoG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEvE,YAAa;QACvBwE,aAAa,EAAE;UACb/B,WAAW,EAAE,IAAI;UACjBtB,QAAQ,EAAE;QACZ,CAAE;QAAAe,QAAA,gBAEF7E,OAAA,CAACR,IAAI,CAAC4H,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,OAAO;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1H,OAAO,EAAE;UAAyB,CAAC,CAAE;UAAA+E,QAAA,eAE/D7E,OAAA,CAACP,KAAK;YAACgI,WAAW,EAAC;UAAmB;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEZlF,OAAA,CAACR,IAAI,CAAC4H,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,UAAU;UAChBI,IAAI,EAAC,yCAAyC;UAAA7C,QAAA,eAE9C7E,OAAA,CAACP,KAAK;YAACgI,WAAW,EAAC;UAAmB;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEZlF,OAAA,CAACR,IAAI,CAAC4H,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,UAAU;UAChBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1H,OAAO,EAAE;UAA2B,CAAC,CAAE;UAAA+E,QAAA,eAEjE7E,OAAA,CAACN,MAAM;YAAC+H,WAAW,EAAC,iBAAiB;YAAA5C,QAAA,gBACnC7E,OAAA,CAACE,MAAM;cAACyH,KAAK,EAAC,aAAa;cAAA9C,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChDlF,OAAA,CAACE,MAAM;cAACyH,KAAK,EAAC,WAAW;cAAA9C,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5ClF,OAAA,CAACE,MAAM;cAACyH,KAAK,EAAC,aAAa;cAAA9C,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChDlF,OAAA,CAACE,MAAM;cAACyH,KAAK,EAAC,MAAM;cAAA9C,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZlF,OAAA,CAACR,IAAI,CAAC4H,IAAI;UACRC,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,SAAS;UACfI,IAAI,EAAC,+BAA+B;UAAA7C,QAAA,eAEpC7E,OAAA,CAACC,QAAQ;YAAC2H,IAAI,EAAE,CAAE;YAACH,WAAW,EAAC;UAAgC;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEZlF,OAAA,CAACR,IAAI,CAAC4H,IAAI;UACRC,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1H,OAAO,EAAE;UAA2B,CAAC,CAAE;UAAA+E,QAAA,eAEjE7E,OAAA,CAACC,QAAQ;YAAC2H,IAAI,EAAE,EAAG;YAACH,WAAW,EAAC;UAAoC;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAEZlF,OAAA;UAAK8E,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrC7E,OAAA,CAACR,IAAI,CAAC4H,IAAI;YACRC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAC,qBAAqB;YAAAzC,QAAA,eAE3B7E,OAAA,CAACJ,WAAW;cAACiI,GAAG,EAAE,CAAE;cAACC,GAAG,EAAE,GAAI;cAACL,WAAW,EAAC;YAAG;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAEZlF,OAAA,CAACR,IAAI,CAAC4H,IAAI;YACRC,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAC,eAAe;YAAAzC,QAAA,eAErB7E,OAAA,CAACP,KAAK;cAACgI,WAAW,EAAC;YAA+B;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENlF,OAAA,CAACR,IAAI,CAAC4H,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,MAAM;UACZI,IAAI,EAAC,sBAAsB;UAAA7C,QAAA,eAE3B7E,OAAA,CAACP,KAAK;YAACgI,WAAW,EAAC;UAAuB;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAEZlF,OAAA,CAACR,IAAI,CAAC4H,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,QAAQ;UACdS,aAAa,EAAC,SAAS;UAAAlD,QAAA,eAEvB7E,OAAA,CAACL,MAAM;YAACqI,eAAe,EAAC,WAAW;YAACC,iBAAiB,EAAC;UAAO;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAEZlF,OAAA;UAAK8E,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD7E,OAAA,CAACZ,MAAM;YAAC4G,OAAO,EAAEA,CAAA,KAAMtF,eAAe,CAAC,KAAK,CAAE;YAAAmE,QAAA,EAAC;UAE/C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlF,OAAA,CAACZ,MAAM;YAAC0G,IAAI,EAAC,SAAS;YAACoC,QAAQ,EAAC,QAAQ;YAAArD,QAAA,GACrClE,YAAY,GAAG,QAAQ,GAAG,QAAQ,EAAC,QACtC;UAAA;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9E,EAAA,CArWID,YAAsB;EAAA,QAKXX,IAAI,CAACsB,OAAO;AAAA;AAAAqH,EAAA,GALvBhI,YAAsB;AAuW5B,eAAeA,YAAY;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}