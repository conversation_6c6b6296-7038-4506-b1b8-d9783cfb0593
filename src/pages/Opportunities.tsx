import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
}

const Opportunities: React.FC = () => {
  const { translations } = useLanguage();
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    type: '',
    location: '',
    isRemote: ''
  });
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchOpportunities();
  }, [filters]);

  const fetchOpportunities = async () => {
    try {
      const params = new URLSearchParams({
        active: 'true',
        orderBy: 'deadline',
        orderDirection: 'ASC',
        ...filters
      });

      const response = await fetch(`/api/opportunities?${params}`);
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.data.opportunities || []);
      } else {
        console.error('Failed to fetch opportunities');
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (searchTerm.trim() === '') {
      fetchOpportunities();
      return;
    }

    try {
      const params = new URLSearchParams({
        q: searchTerm,
        ...filters
      });

      const response = await fetch(`/api/opportunities/search?${params}`);
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.data.opportunities || []);
      }
    } catch (error) {
      console.error('Error searching opportunities:', error);
    }
  };

  const getTypeIcon = (type: string): string => {
    const icons = {
      internship: '🎓',
      training: '📚',
      conference: '🎤',
      workshop: '🔧',
      competition: '🏆'
    };
    return icons[type as keyof typeof icons] || '📋';
  };

  const getTypeColor = (type: string): string => {
    const colors = {
      internship: 'bg-blue-100 text-blue-800',
      training: 'bg-green-100 text-green-800',
      conference: 'bg-purple-100 text-purple-800',
      workshop: 'bg-orange-100 text-orange-800',
      competition: 'bg-red-100 text-red-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const isExpired = (deadline: string): boolean => {
    return new Date(deadline) < new Date();
  };

  const getDaysUntilDeadline = (deadline: string): number => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des opportunités...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {translations.opportunities.title}
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              {translations.opportunities.subtitle}
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="md:col-span-2">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Rechercher des opportunités..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <select
                value={filters.type}
                onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">{translations.opportunities.filters.all}</option>
                <option value="internship">{translations.opportunities.types.internship}</option>
                <option value="training">{translations.opportunities.types.training}</option>
                <option value="conference">{translations.opportunities.types.conference}</option>
                <option value="workshop">{translations.opportunities.types.workshop}</option>
                <option value="competition">{translations.opportunities.types.competition}</option>
              </select>
            </div>
            
            <div>
              <select
                value={filters.isRemote}
                onChange={(e) => setFilters({ ...filters, isRemote: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Présentiel et distanciel</option>
                <option value="true">Distanciel uniquement</option>
                <option value="false">Présentiel uniquement</option>
              </select>
            </div>
          </div>
          
          <div className="flex justify-center">
            <button
              onClick={handleSearch}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              Rechercher
            </button>
          </div>
        </div>
      </div>

      {/* Type Quick Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {Object.entries(translations.opportunities.types).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setFilters({ ...filters, type: filters.type === key ? '' : key })}
              className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                filters.type === key
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
              }`}
            >
              <div className="text-2xl mb-2">{getTypeIcon(key)}</div>
              <div className="font-medium text-sm">{label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Opportunities Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {opportunities.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Aucune opportunité trouvée
            </h3>
            <p className="text-gray-600">
              Essayez de modifier vos filtres ou votre recherche.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {opportunities.map((opportunity) => {
              const expired = isExpired(opportunity.deadline);
              const daysLeft = getDaysUntilDeadline(opportunity.deadline);
              
              return (
                <div
                  key={opportunity.id}
                  className={`bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 ${
                    expired ? 'opacity-75' : 'hover:border-blue-200'
                  }`}
                >
                  {opportunity.thumbnail && (
                    <div className="aspect-w-16 aspect-h-9 overflow-hidden">
                      <img
                        src={opportunity.thumbnail}
                        alt={opportunity.title}
                        className="w-full h-48 object-cover"
                      />
                    </div>
                  )}
                  
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(opportunity.type)}`}>
                        <span className="mr-1">{getTypeIcon(opportunity.type)}</span>
                        {translations.opportunities.types[opportunity.type]}
                      </span>
                      
                      {opportunity.isRemote && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          🌐 Distanciel
                        </span>
                      )}
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                      {opportunity.title}
                    </h3>
                    
                    <p className="text-gray-600 text-sm mb-3 line-clamp-3">
                      {opportunity.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        {opportunity.organization}
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-600">
                        <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        {opportunity.location}
                      </div>
                      
                      <div className={`flex items-center text-sm ${expired ? 'text-red-600' : daysLeft <= 7 ? 'text-orange-600' : 'text-gray-600'}`}>
                        <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {expired ? (
                          <span className="font-medium">{translations.opportunities.expired}</span>
                        ) : (
                          <span>
                            Date limite: {formatDate(opportunity.deadline)}
                            {daysLeft <= 7 && (
                              <span className="ml-1 font-medium">
                                ({daysLeft} jour{daysLeft !== 1 ? 's' : ''} restant{daysLeft !== 1 ? 's' : ''})
                              </span>
                            )}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {opportunity.tags && opportunity.tags.length > 0 && (
                      <div className="mb-4 flex flex-wrap gap-1">
                        {opportunity.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <Link
                        to={`/opportunities/${opportunity.id}`}
                        className="text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors duration-200"
                      >
                        {translations.opportunities.viewDetails}
                      </Link>
                      
                      {opportunity.applicationLink && !expired && (
                        <a
                          href={opportunity.applicationLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200"
                        >
                          {translations.opportunities.apply}
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default Opportunities;
