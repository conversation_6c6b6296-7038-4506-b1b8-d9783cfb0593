{"name": "scholarship-portal-backend", "version": "1.0.0", "description": "Backend for the Scholarship Portal", "main": "src/index.ts", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "create-admin": "ts-node src/scripts/createAdmin.ts", "secure-admin": "ts-node src/scripts/secureAdminSetup.ts", "migrate": "ts-node src/database/migrate.ts", "migrate:thumbnails": "ts-node scripts/migrate-thumbnails.ts"}, "keywords": ["scholarship", "education", "nodejs", "express"], "author": "", "license": "ISC", "dependencies": {"@types/mongoose": "^5.11.96", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/react-helmet": "^6.1.11", "@types/sequelize": "^4.28.20", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.8.7", "morgan": "^1.10.0", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "otplib": "^12.0.1", "pg": "^8.16.3", "qrcode": "^1.5.4", "react-helmet": "^6.1.0", "recharts": "^2.15.3", "winston": "^3.17.0", "xss": "^1.0.15"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.12", "@types/node": "^18.15.11", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "eslint": "^8.37.0", "jest": "^29.5.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^2.0.22", "supertest": "^7.1.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.3"}}