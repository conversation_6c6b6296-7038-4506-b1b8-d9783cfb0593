/**
 * Guide Model
 *
 * This model handles all database operations for guides (CV writing, document preparation, etc.)
 */

import { query } from '../config/database';

export interface GuideData {
  id?: number;
  title: string;
  content: string;
  category: 'application' | 'documents' | 'preparation' | 'tips';
  slug: string;
  excerpt?: string;
  thumbnail?: string;
  isPublished: boolean;
  readTime?: number; // in minutes
  tags?: string[];
  createdBy?: number | null;
  createdByAdmin?: number | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Guide {
  /**
   * Create a new guide
   */
  static async create(guideData: Omit<GuideData, 'id' | 'createdAt' | 'updatedAt'>): Promise<GuideData> {
    const result = await query(`
      INSERT INTO guides (
        title, content, category, slug, excerpt, thumbnail, is_published,
        read_time, tags, created_by, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      guideData.title,
      guideData.content,
      guideData.category,
      guideData.slug,
      guideData.excerpt,
      guideData.thumbnail,
      guideData.isPublished !== undefined ? guideData.isPublished : true,
      guideData.readTime,
      guideData.tags ? JSON.stringify(guideData.tags) : null,
      guideData.createdBy,
      guideData.createdByAdmin,
    ]);

    return this.mapRowToGuide(result.rows[0]);
  }

  /**
   * Get all guides
   */
  static async findAll(options?: {
    limit?: number;
    offset?: number;
    isPublished?: boolean;
    category?: string;
    orderBy?: 'created_at' | 'title' | 'read_time';
    orderDirection?: 'ASC' | 'DESC';
  }): Promise<GuideData[]> {
    let queryText = 'SELECT * FROM guides WHERE 1=1';
    const params: any[] = [];
    let paramCount = 1;

    // Add filters
    if (options?.isPublished !== undefined) {
      queryText += ` AND is_published = $${paramCount}`;
      params.push(options.isPublished);
      paramCount++;
    }

    if (options?.category) {
      queryText += ` AND category = $${paramCount}`;
      params.push(options.category);
      paramCount++;
    }

    // Add ordering
    const orderBy = options?.orderBy || 'created_at';
    const orderDirection = options?.orderDirection || 'DESC';
    queryText += ` ORDER BY ${orderBy} ${orderDirection}`;

    // Add pagination
    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;
    }

    if (options?.offset) {
      queryText += ` OFFSET $${paramCount}`;
      params.push(options.offset);
      paramCount++;
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToGuide(row));
  }

  /**
   * Get guide by ID
   */
  static async findById(id: number): Promise<GuideData | null> {
    const result = await query('SELECT * FROM guides WHERE id = $1', [id]);
    return result.rows.length > 0 ? this.mapRowToGuide(result.rows[0]) : null;
  }

  /**
   * Get guide by slug
   */
  static async findBySlug(slug: string): Promise<GuideData | null> {
    const result = await query('SELECT * FROM guides WHERE slug = $1', [slug]);
    return result.rows.length > 0 ? this.mapRowToGuide(result.rows[0]) : null;
  }

  /**
   * Update guide
   */
  static async update(id: number, updateData: Partial<GuideData>): Promise<GuideData | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
        const dbField = this.mapFieldToColumn(key);
        fields.push(`${dbField} = $${paramCount}`);
        
        if (key === 'tags' && Array.isArray(value)) {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }
        paramCount++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    fields.push(`updated_at = NOW()`);
    values.push(id);

    const queryText = `
      UPDATE guides 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(queryText, values);
    return result.rows.length > 0 ? this.mapRowToGuide(result.rows[0]) : null;
  }

  /**
   * Delete guide
   */
  static async delete(id: number): Promise<boolean> {
    const result = await query('DELETE FROM guides WHERE id = $1', [id]);
    return result.rowCount > 0;
  }

  /**
   * Get guides by category
   */
  static async findByCategory(category: string, limit?: number): Promise<GuideData[]> {
    let queryText = 'SELECT * FROM guides WHERE category = $1 AND is_published = true ORDER BY created_at DESC';
    const params = [category];

    if (limit) {
      queryText += ' LIMIT $2';
      params.push(limit);
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToGuide(row));
  }

  /**
   * Search guides
   */
  static async search(searchTerm: string, options?: {
    category?: string;
    limit?: number;
    offset?: number;
  }): Promise<GuideData[]> {
    let queryText = `
      SELECT * FROM guides 
      WHERE is_published = true 
      AND (title ILIKE $1 OR content ILIKE $1 OR excerpt ILIKE $1)
    `;
    const params = [`%${searchTerm}%`];
    let paramCount = 2;

    if (options?.category) {
      queryText += ` AND category = $${paramCount}`;
      params.push(options.category);
      paramCount++;
    }

    queryText += ' ORDER BY created_at DESC';

    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;
    }

    if (options?.offset) {
      queryText += ` OFFSET $${paramCount}`;
      params.push(options.offset);
      paramCount++;
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToGuide(row));
  }

  /**
   * Map field names to database column names
   */
  private static mapFieldToColumn(field: string): string {
    const fieldMap: { [key: string]: string } = {
      isPublished: 'is_published',
      readTime: 'read_time',
      createdBy: 'created_by',
      createdByAdmin: 'created_by_admin',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    };
    return fieldMap[field] || field;
  }

  /**
   * Map database row to GuideData
   */
  private static mapRowToGuide(row: any): GuideData {
    return {
      id: row.id,
      title: row.title,
      content: row.content,
      category: row.category,
      slug: row.slug,
      excerpt: row.excerpt,
      thumbnail: row.thumbnail,
      isPublished: row.is_published,
      readTime: row.read_time,
      tags: row.tags ? JSON.parse(row.tags) : [],
      createdBy: row.created_by,
      createdByAdmin: row.created_by_admin,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}
