/**
 * Opportunity Model
 *
 * This model handles all database operations for opportunities (internships, training, conferences, etc.)
 */

import { query } from '../config/database';

export interface OpportunityData {
  id?: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: Date;
  startDate?: Date;
  endDate?: Date;
  applicationLink?: string;
  requirements?: string;
  benefits?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
  contactEmail?: string;
  website?: string;
  createdBy?: number | null;
  createdByAdmin?: number | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export class Opportunity {
  /**
   * Create a new opportunity
   */
  static async create(opportunityData: Omit<OpportunityData, 'id' | 'createdAt' | 'updatedAt'>): Promise<OpportunityData> {
    const result = await query(`
      INSERT INTO opportunities (
        title, description, type, organization, location, is_remote, deadline,
        start_date, end_date, application_link, requirements, benefits, thumbnail,
        is_active, tags, contact_email, website, created_by, created_by_admin
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
      RETURNING *
    `, [
      opportunityData.title,
      opportunityData.description,
      opportunityData.type,
      opportunityData.organization,
      opportunityData.location,
      opportunityData.isRemote !== undefined ? opportunityData.isRemote : false,
      opportunityData.deadline,
      opportunityData.startDate,
      opportunityData.endDate,
      opportunityData.applicationLink,
      opportunityData.requirements,
      opportunityData.benefits,
      opportunityData.thumbnail,
      opportunityData.isActive !== undefined ? opportunityData.isActive : true,
      opportunityData.tags ? JSON.stringify(opportunityData.tags) : null,
      opportunityData.contactEmail,
      opportunityData.website,
      opportunityData.createdBy,
      opportunityData.createdByAdmin,
    ]);

    return this.mapRowToOpportunity(result.rows[0]);
  }

  /**
   * Get all opportunities
   */
  static async findAll(options?: {
    limit?: number;
    offset?: number;
    isActive?: boolean;
    type?: string;
    location?: string;
    isRemote?: boolean;
    orderBy?: 'created_at' | 'deadline' | 'title' | 'start_date';
    orderDirection?: 'ASC' | 'DESC';
  }): Promise<OpportunityData[]> {
    let queryText = 'SELECT * FROM opportunities WHERE 1=1';
    const params: any[] = [];
    let paramCount = 1;

    // Add filters
    if (options?.isActive !== undefined) {
      queryText += ` AND is_active = $${paramCount}`;
      params.push(options.isActive);
      paramCount++;
    }

    if (options?.type) {
      queryText += ` AND type = $${paramCount}`;
      params.push(options.type);
      paramCount++;
    }

    if (options?.location) {
      queryText += ` AND location ILIKE $${paramCount}`;
      params.push(`%${options.location}%`);
      paramCount++;
    }

    if (options?.isRemote !== undefined) {
      queryText += ` AND is_remote = $${paramCount}`;
      params.push(options.isRemote);
      paramCount++;
    }

    // Add ordering
    const orderBy = options?.orderBy || 'created_at';
    const orderDirection = options?.orderDirection || 'DESC';
    queryText += ` ORDER BY ${orderBy} ${orderDirection}`;

    // Add pagination
    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;
    }

    if (options?.offset) {
      queryText += ` OFFSET $${paramCount}`;
      params.push(options.offset);
      paramCount++;
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToOpportunity(row));
  }

  /**
   * Get opportunity by ID
   */
  static async findById(id: number): Promise<OpportunityData | null> {
    const result = await query('SELECT * FROM opportunities WHERE id = $1', [id]);
    return result.rows.length > 0 ? this.mapRowToOpportunity(result.rows[0]) : null;
  }

  /**
   * Update opportunity
   */
  static async update(id: number, updateData: Partial<OpportunityData>): Promise<OpportunityData | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
        const dbField = this.mapFieldToColumn(key);
        fields.push(`${dbField} = $${paramCount}`);
        
        if (key === 'tags' && Array.isArray(value)) {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }
        paramCount++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    fields.push(`updated_at = NOW()`);
    values.push(id);

    const queryText = `
      UPDATE opportunities 
      SET ${fields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(queryText, values);
    return result.rows.length > 0 ? this.mapRowToOpportunity(result.rows[0]) : null;
  }

  /**
   * Delete opportunity
   */
  static async delete(id: number): Promise<boolean> {
    const result = await query('DELETE FROM opportunities WHERE id = $1', [id]);
    return result.rowCount > 0;
  }

  /**
   * Get opportunities by type
   */
  static async findByType(type: string, limit?: number): Promise<OpportunityData[]> {
    let queryText = 'SELECT * FROM opportunities WHERE type = $1 AND is_active = true ORDER BY deadline ASC';
    const params = [type];

    if (limit) {
      queryText += ' LIMIT $2';
      params.push(limit);
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToOpportunity(row));
  }

  /**
   * Get active opportunities (not expired)
   */
  static async findActive(limit?: number): Promise<OpportunityData[]> {
    let queryText = 'SELECT * FROM opportunities WHERE is_active = true AND deadline > NOW() ORDER BY deadline ASC';
    const params: any[] = [];

    if (limit) {
      queryText += ' LIMIT $1';
      params.push(limit);
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToOpportunity(row));
  }

  /**
   * Search opportunities
   */
  static async search(searchTerm: string, options?: {
    type?: string;
    location?: string;
    isRemote?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<OpportunityData[]> {
    let queryText = `
      SELECT * FROM opportunities 
      WHERE is_active = true 
      AND (title ILIKE $1 OR description ILIKE $1 OR organization ILIKE $1)
    `;
    const params = [`%${searchTerm}%`];
    let paramCount = 2;

    if (options?.type) {
      queryText += ` AND type = $${paramCount}`;
      params.push(options.type);
      paramCount++;
    }

    if (options?.location) {
      queryText += ` AND location ILIKE $${paramCount}`;
      params.push(`%${options.location}%`);
      paramCount++;
    }

    if (options?.isRemote !== undefined) {
      queryText += ` AND is_remote = $${paramCount}`;
      params.push(options.isRemote);
      paramCount++;
    }

    queryText += ' ORDER BY deadline ASC';

    if (options?.limit) {
      queryText += ` LIMIT $${paramCount}`;
      params.push(options.limit);
      paramCount++;
    }

    if (options?.offset) {
      queryText += ` OFFSET $${paramCount}`;
      params.push(options.offset);
      paramCount++;
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToOpportunity(row));
  }

  /**
   * Map field names to database column names
   */
  private static mapFieldToColumn(field: string): string {
    const fieldMap: { [key: string]: string } = {
      isRemote: 'is_remote',
      startDate: 'start_date',
      endDate: 'end_date',
      applicationLink: 'application_link',
      isActive: 'is_active',
      contactEmail: 'contact_email',
      createdBy: 'created_by',
      createdByAdmin: 'created_by_admin',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    };
    return fieldMap[field] || field;
  }

  /**
   * Map database row to OpportunityData
   */
  private static mapRowToOpportunity(row: any): OpportunityData {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      type: row.type,
      organization: row.organization,
      location: row.location,
      isRemote: row.is_remote,
      deadline: row.deadline,
      startDate: row.start_date,
      endDate: row.end_date,
      applicationLink: row.application_link,
      requirements: row.requirements,
      benefits: row.benefits,
      thumbnail: row.thumbnail,
      isActive: row.is_active,
      tags: row.tags ? JSON.parse(row.tags) : [],
      contactEmail: row.contact_email,
      website: row.website,
      createdBy: row.created_by,
      createdByAdmin: row.created_by_admin,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}
