/**
 * Country Controller
 * 
 * Handles country-related operations and scholarship filtering by country
 */

import { Request, Response } from 'express';
import { Scholarship } from '../models/Scholarship';
import { sendSuccess, sendError } from '../utils/response.util';

/**
 * Get all countries with scholarship counts
 */
export const getAllCountries = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get distinct countries from scholarships with counts
    const result = await Scholarship.getCountriesWithCounts();
    
    sendSuccess(res, 'Countries retrieved successfully', result);
  } catch (error) {
    console.error('Error fetching countries:', error);
    sendError(res, 'Failed to fetch countries', error);
  }
};

/**
 * Get scholarships by country
 */
export const getScholarshipsByCountry = async (req: Request, res: Response): Promise<void> => {
  try {
    const { country } = req.params;
    const {
      page = 1,
      limit = 10,
      level,
      isOpen,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = req.query;

    if (!country) {
      sendError(res, 'Country parameter is required', null, 400);
      return;
    }

    const offset = (Number(page) - 1) * Number(limit);
    
    const options = {
      limit: Number(limit),
      offset,
      country: decodeURIComponent(country),
      level: level as string,
      isOpen: isOpen === 'true' ? true : isOpen === 'false' ? false : undefined,
      orderBy: orderBy as 'created_at' | 'deadline' | 'title',
      orderDirection: orderDirection as 'ASC' | 'DESC'
    };

    const scholarships = await Scholarship.findAll(options);
    
    sendSuccess(res, 'Scholarships retrieved successfully', {
      country: decodeURIComponent(country),
      scholarships,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: scholarships.length
      }
    });
  } catch (error) {
    console.error('Error fetching scholarships by country:', error);
    sendError(res, 'Failed to fetch scholarships', error);
  }
};

/**
 * Get country statistics
 */
export const getCountryStatistics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { country } = req.params;

    if (!country) {
      sendError(res, 'Country parameter is required', null, 400);
      return;
    }

    const decodedCountry = decodeURIComponent(country);
    
    // Get total scholarships for this country
    const totalScholarships = await Scholarship.findAll({ country: decodedCountry });
    
    // Get open scholarships for this country
    const openScholarships = await Scholarship.findAll({ 
      country: decodedCountry, 
      isOpen: true 
    });
    
    // Get scholarships by level for this country
    const levels = ['Bachelor', 'Master', 'PhD', 'Postdoc'];
    const scholarshipsByLevel = await Promise.all(
      levels.map(async (level) => {
        const scholarships = await Scholarship.findAll({ 
          country: decodedCountry, 
          level 
        });
        return {
          level,
          count: scholarships.length
        };
      })
    );

    const statistics = {
      country: decodedCountry,
      totalScholarships: totalScholarships.length,
      openScholarships: openScholarships.length,
      closedScholarships: totalScholarships.length - openScholarships.length,
      scholarshipsByLevel: scholarshipsByLevel.filter(item => item.count > 0)
    };
    
    sendSuccess(res, 'Country statistics retrieved successfully', statistics);
  } catch (error) {
    console.error('Error fetching country statistics:', error);
    sendError(res, 'Failed to fetch country statistics', error);
  }
};

/**
 * Search countries
 */
export const searchCountries = async (req: Request, res: Response): Promise<void> => {
  try {
    const { q } = req.query;
    
    if (!q || typeof q !== 'string') {
      sendError(res, 'Search query is required', null, 400);
      return;
    }

    const countries = await Scholarship.searchCountries(q);
    
    sendSuccess(res, 'Country search completed successfully', {
      query: q,
      countries
    });
  } catch (error) {
    console.error('Error searching countries:', error);
    sendError(res, 'Failed to search countries', error);
  }
};
